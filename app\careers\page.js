"use client";

import React, { useState, useEffect } from "react";
import { motion } from "framer-motion";
import {
  Card,
  CardContent,
  CardDescription,
  Card<PERSON>ooter,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { Tabs, Ta<PERSON><PERSON>ontent, Ta<PERSON>List, TabsTrigger } from "@/components/ui/tabs";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import {
  Accordion,
  AccordionContent,
  AccordionItem,
  AccordionTrigger,
} from "@/components/ui/accordion";
import { Badge } from "@/components/ui/badge";
import { Check } from "lucide-react";
import Image from "next/image";

const CareersPage = () => {
  const [activeJobCategory, setActiveJobCategory] = useState("all");
  const [searchTerm, setSearchTerm] = useState("");
  const [filteredJobs, setFilteredJobs] = useState([]);

  // Sample job listings data
  const jobs = [
    {
      id: 1,
      title: "Senior AI/ML Engineer",
      department: "Engineering",
      category: "ai-ml",
      location: "Bengaluru, India (Hybrid)",
      type: "Full-time",
      experience: "4-6 years",
      description:
        "Lead the development of cutting-edge AI and ML solutions for our clients, focusing on natural language processing and computer vision applications.",
      skills: [
        "Python",
        "TensorFlow",
        "PyTorch",
        "Deep Learning",
        "NLP",
        "Computer Vision",
      ],
      responsibilities: [
        "Design and implement AI/ML algorithms for client projects",
        "Collaborate with cross-functional teams to deliver end-to-end solutions",
        "Stay current with latest AI research and implement novel approaches",
        "Mentor junior engineers and contribute to knowledge sharing",
      ],
    },
    {
      id: 2,
      title: "Blockchain Developer",
      department: "Engineering",
      category: "web3",
      location: "Remote (India)",
      type: "Full-time",
      experience: "3-5 years",
      description:
        "Develop and implement blockchain solutions for our enterprise clients, with focus on smart contracts and decentralized applications.",
      skills: [
        "Solidity",
        "Ethereum",
        "Web3.js",
        "Smart Contracts",
        "DApps",
        "Cryptography",
      ],
      responsibilities: [
        "Develop and deploy smart contracts on various blockchain platforms",
        "Build secure and scalable decentralized applications",
        "Integrate blockchain solutions with existing client systems",
        "Research and implement best practices in blockchain security",
      ],
    },
    {
      id: 3,
      title: "Business Strategy Consultant",
      department: "Consulting",
      category: "business",
      location: "Mumbai, India (Hybrid)",
      type: "Full-time",
      experience: "5-8 years",
      description:
        "Help our clients develop and implement effective business strategies, with a focus on digital transformation and innovation.",
      skills: [
        "Strategic Planning",
        "Market Analysis",
        "Digital Transformation",
        "Business Development",
        "Project Management",
      ],
      responsibilities: [
        "Conduct in-depth market analysis and competitive intelligence",
        "Develop strategic plans and roadmaps for clients",
        "Present findings and recommendations to C-level executives",
        "Implement and track strategic initiatives",
      ],
    },
    {
      id: 4,
      title: "Data Scientist",
      department: "Data",
      category: "data",
      location: "Hyderabad, India (On-site)",
      type: "Full-time",
      experience: "2-4 years",
      description:
        "Transform complex data into actionable insights for our clients, working with large datasets and advanced analytics tools.",
      skills: [
        "Python",
        "R",
        "SQL",
        "Data Visualization",
        "Statistical Analysis",
        "Machine Learning",
      ],
      responsibilities: [
        "Analyze large datasets to extract valuable insights",
        "Build predictive models and data visualization dashboards",
        "Translate business problems into data questions",
        "Present findings to technical and non-technical stakeholders",
      ],
    },
    {
      id: 5,
      title: "Full Stack Developer",
      department: "Engineering",
      category: "web-dev",
      location: "Pune, India (Hybrid)",
      type: "Full-time",
      experience: "3-5 years",
      description:
        "Build modern, responsive web applications for our clients using cutting-edge technologies and frameworks.",
      skills: [
        "React.js",
        "Node.js",
        "Next.js",
        "TypeScript",
        "RESTful APIs",
        "MongoDB",
      ],
      responsibilities: [
        "Develop and maintain client web applications",
        "Implement responsive UI/UX designs",
        "Build scalable backend services and APIs",
        "Collaborate with design and product teams",
      ],
    },
    {
      id: 6,
      title: "Cybersecurity Analyst",
      department: "Security",
      category: "security",
      location: "Delhi, India (On-site)",
      type: "Full-time",
      experience: "3-6 years",
      description:
        "Protect our clientsWS digital assets and infrastructure from cyber threats and vulnerabilities.",
      skills: [
        "Network Security",
        "Penetration Testing",
        "Vulnerability Assessment",
        "Security Frameworks",
        "Incident Response",
      ],
      responsibilities: [
        "Conduct security assessments and penetration tests",
        "Develop and implement security protocols and policies",
        "Monitor systems for security breaches and respond to incidents",
        "Educate client teams on security best practices",
      ],
    },
    {
      id: 7,
      title: "Product Manager",
      department: "Product",
      category: "business",
      location: "Remote (India)",
      type: "Full-time",
      experience: "4-7 years",
      description:
        "Lead the development and execution of product strategies, working closely with engineering, design, and client teams.",
      skills: [
        "Product Strategy",
        "Agile Methodologies",
        "User Research",
        "Roadmapping",
        "Stakeholder Management",
      ],
      responsibilities: [
        "Define product vision, strategy, and roadmap",
        "Gather and prioritize product requirements",
        "Work with engineering teams to deliver features",
        "Analyze market trends and competitive landscape",
      ],
    },
    {
      id: 8,
      title: "Digital Marketing Specialist",
      department: "Marketing",
      category: "marketing",
      location: "Bengaluru, India (Hybrid)",
      type: "Full-time",
      experience: "2-4 years",
      description:
        "Create and implement digital marketing strategies that drive growth and engagement for our clients.",
      skills: [
        "SEO/SEM",
        "Social Media Marketing",
        "Content Marketing",
        "Analytics",
        "Email Marketing",
      ],
      responsibilities: [
        "Develop and execute digital marketing campaigns",
        "Optimize client websites for search engines",
        "Analyze campaign performance and provide recommendations",
        "Manage social media presence and content calendars",
      ],
    },
  ];

  // Benefits data
  const benefits = [
    {
      title: "Competitive Compensation",
      description:
        "Industry-leading salary packages with performance bonuses and ESOPs for eligible roles",
      icon: "💰",
    },
    {
      title: "Health & Wellness",
      description:
        "Comprehensive health insurance for you and your family, mental health support, and wellness programs",
      icon: "🏥",
    },
    {
      title: "Flexible Work",
      description:
        "Remote, hybrid, and flexible work options with generous PTO policy and paid sabbaticals",
      icon: "🏡",
    },
    {
      title: "Learning & Growth",
      description:
        "Continuous learning budget, conference allowances, certifications, and mentorship programs",
      icon: "📚",
    },
    {
      title: "Global Exposure",
      description:
        "Opportunity to work with international clients and on cutting-edge projects across industries",
      icon: "🌍",
    },
    {
      title: "Work-Life Balance",
      description:
        "Flexible hours, no-meeting Fridays, and respect for your personal time and commitments",
      icon: "⚖️",
    },
  ];

  // Testimonials data
  const testimonials = [
    {
      quote:
        "Joining OfStartup was the best career decision I've made. I get to work on cutting-edge AI projects while continuously growing my skills with a team of brilliant minds.",
      author: "Priya Sharma",
      role: "Senior AI Engineer",
      duration: "3 years at OfStartup",
      image: "/api/placeholder/60/60",
    },
    {
      quote:
        "The work culture at OfStartup is incredible. We're solving real business problems with technology while maintaining a healthy work-life balance.",
      author: "Raj Patel",
      role: "Full Stack Developer",
      duration: "2 years at OfStartup",
      image: "/api/placeholder/60/60",
    },
    {
      quote:
        "As a business consultant, I appreciate how OfStartup encourages cross-functional collaboration. We deliver truly holistic solutions because our tech and business teams work so closely together.",
      author: "Ananya Singh",
      role: "Business Strategy Lead",
      duration: "4 years at OfStartup",
      image: "/api/placeholder/60/60",
    },
  ];

  // FAQ data
  const faqs = [
    {
      question: "What is the interview process like at OfStartup?",
      answer:
        "Our interview process typically includes 3-4 stages: an initial screening call, a technical/role-specific assessment, a panel interview with the team, and a final discussion with leadership. We aim to make the process transparent and provide feedback at each stage.",
    },
    {
      question: "Do you offer internship opportunities?",
      answer:
        "Yes, we offer paid internships across various departments throughout the year. Internships typically last 3-6 months with the possibility of conversion to full-time roles for exceptional performers.",
    },
    {
      question: "What is your remote work policy?",
      answer:
        "We offer flexible working arrangements including remote, hybrid, and on-site options depending on the role and team requirements. We believe in results-oriented work and trust our employees to manage their time effectively.",
    },
    {
      question: "How do you support career growth and development?",
      answer:
        "We provide a dedicated learning budget, regular skill development workshops, mentorship programs, and clear career progression paths. We encourage employees to explore different areas within the company and support internal mobility.",
    },
    {
      question: "What is the company culture like?",
      answer:
        "We foster a culture of innovation, collaboration, and continuous learning. We value diversity of thought, encourage experimentation, and maintain a healthy work-life balance. Our teams are supportive, driven, and passionate about solving complex challenges.",
    },
  ];

  // Filter jobs based on category and search term
  useEffect(() => {
    let result = jobs;

    if (activeJobCategory !== "all") {
      result = result.filter((job) => job.category === activeJobCategory);
    }

    if (searchTerm) {
      const term = searchTerm.toLowerCase();
      result = result.filter(
        (job) =>
          job.title.toLowerCase().includes(term) ||
          job.description.toLowerCase().includes(term) ||
          job.skills.some((skill) => skill.toLowerCase().includes(term))
      );
    }

    setFilteredJobs(result);
  }, [activeJobCategory, searchTerm]);

  // Animation variants
  const containerVariants = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        staggerChildren: 0.1,
        delayChildren: 0.2,
      },
    },
  };

  const itemVariants = {
    hidden: { y: 20, opacity: 0 },
    visible: {
      y: 0,
      opacity: 1,
      transition: { type: "spring", stiffness: 100 },
    },
  };

  return (
    <div className="min-h-screen bg-black text-gray-200">
      {/* Hero Section */}
      <section className="relative overflow-hidden py-20 lg:py-32">
        <div className="absolute inset-0 z-0">
          <div className="absolute inset-0 bg-gradient-to-r from-purple-900/30 to-blue-900/20 z-10"></div>
          <div className="absolute top-0 left-0 right-0 h-px bg-gradient-to-r from-transparent via-purple-500 to-transparent"></div>
          <div className="absolute bottom-0 left-0 right-0 h-px bg-gradient-to-r from-transparent via-blue-500 to-transparent"></div>
          <div className="absolute w-full h-full bg-[radial-gradient(circle_at_30%_30%,rgba(64,25,109,0.5)_0%,transparent_30%)]"></div>
          <div className="absolute w-full h-full bg-[radial-gradient(circle_at_70%_70%,rgba(25,65,109,0.5)_0%,transparent_30%)]"></div>
        </div>

        <div className="container mx-auto px-4 relative z-10">
          <div className="max-w-4xl mx-auto text-center">
            <motion.div
              initial={{ opacity: 0, y: 30 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6 }}
            >
              <h1 className="text-4xl md:text-6xl font-bold mb-6 bg-clip-text text-transparent bg-gradient-to-r from-blue-400 to-purple-500">
                Join Our Mission to Transform Business & Technology
              </h1>
              <p className="text-xl md:text-2xl text-gray-300 mb-8">
                At OfStartup, we&apos;re building the future of technology
                solutions. Come shape the digital landscape with us.
              </p>
              <div className="flex flex-col sm:flex-row gap-4 justify-center">
                <Button className="bg-gradient-to-r from-blue-600 to-purple-600 hover:from-blue-700 hover:to-purple-700 text-white px-8 py-6 rounded-lg text-lg">
                  View Open Positions
                </Button>
                <Button
                  variant="outline"
                  className="border-gray-600 text-gray-300 hover:bg-gray-800 px-8 py-6 rounded-lg text-lg"
                >
                  Our Culture
                </Button>
              </div>
            </motion.div>
          </div>
        </div>
      </section>

      {/* Stats Section */}
      <section className="py-16 bg-gradient-to-b from-black to-gray-900">
        <div className="container mx-auto px-4">
          <motion.div
            className="grid grid-cols-2 md:grid-cols-4 gap-6 text-center"
            variants={containerVariants}
            initial="hidden"
            whileInView="visible"
            viewport={{ once: true, amount: 0.3 }}
          >
            <motion.div className="p-6" variants={itemVariants}>
              <p className="text-4xl font-bold text-blue-400 mb-2">150+</p>
              <p className="text-lg text-gray-400">Team Members</p>
            </motion.div>
            <motion.div className="p-6" variants={itemVariants}>
              <p className="text-4xl font-bold text-purple-400 mb-2">20+</p>
              <p className="text-lg text-gray-400">Countries Represented</p>
            </motion.div>
            <motion.div className="p-6" variants={itemVariants}>
              <p className="text-4xl font-bold text-blue-400 mb-2">4.8/5</p>
              <p className="text-lg text-gray-400">Employee Satisfaction</p>
            </motion.div>
            <motion.div className="p-6" variants={itemVariants}>
              <p className="text-4xl font-bold text-purple-400 mb-2">92%</p>
              <p className="text-lg text-gray-400">Employee Retention</p>
            </motion.div>
          </motion.div>
        </div>
      </section>

      {/* Open Positions Section */}
      <section className="py-16 bg-gray-900">
        <div className="container mx-auto px-4">
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            viewport={{ once: true }}
            transition={{ duration: 0.6 }}
            className="max-w-4xl mx-auto mb-12 text-center"
          >
            <h2 className="text-3xl md:text-4xl font-bold mb-4 text-white">
              Open Positions
            </h2>
            <p className="text-xl text-gray-400">
              Join our team of innovators and problem solvers working on
              cutting-edge solutions
            </p>
          </motion.div>

          <div className="max-w-6xl mx-auto">
            {/* Search and Filter */}
            <div className="flex flex-col md:flex-row gap-4 mb-8">
              <div className="flex-1">
                <Input
                  type="text"
                  placeholder="Search by title, skill, or keyword..."
                  className="bg-gray-800 border-gray-700 text-gray-200"
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                />
              </div>
              <div className="w-full md:w-64">
                <Select onValueChange={setActiveJobCategory} defaultValue="all">
                  <SelectTrigger className="bg-gray-800 border-gray-700 text-gray-200">
                    <SelectValue placeholder="Filter by category" />
                  </SelectTrigger>
                  <SelectContent className="bg-gray-800 border-gray-700 text-gray-200">
                    <SelectItem value="all">All Categories</SelectItem>
                    <SelectItem value="ai-ml">AI/ML</SelectItem>
                    <SelectItem value="web-dev">Web Development</SelectItem>
                    <SelectItem value="data">Data Analytics</SelectItem>
                    <SelectItem value="business">Business Strategy</SelectItem>
                    <SelectItem value="web3">Web3 & Blockchain</SelectItem>
                    <SelectItem value="security">Security</SelectItem>
                    <SelectItem value="marketing">Marketing</SelectItem>
                  </SelectContent>
                </Select>
              </div>
            </div>

            {/* Job Listings */}
            <motion.div
              variants={containerVariants}
              initial="hidden"
              animate="visible"
              className="grid grid-cols-1 gap-6"
            >
              {filteredJobs.length > 0 ? (
                filteredJobs.map((job) => (
                  <motion.div key={job.id} variants={itemVariants}>
                    <Card className="bg-gray-800 border-gray-700 hover:border-blue-500 transition-all duration-300">
                      <CardHeader>
                        <div className="flex flex-col md:flex-row md:justify-between md:items-start gap-4">
                          <div>
                            <CardTitle className="text-xl text-white mb-2">
                              {job.title}
                            </CardTitle>
                            <CardDescription className="text-gray-400">
                              {job.department} • {job.location} • {job.type} •{" "}
                              {job.experience}
                            </CardDescription>
                          </div>
                          <Button className="bg-gradient-to-r from-blue-600 to-purple-600 hover:from-blue-700 hover:to-purple-700 text-white">
                            Apply Now
                          </Button>
                        </div>
                      </CardHeader>
                      <CardContent>
                        <p className="text-gray-300 mb-4">{job.description}</p>
                        <div className="mb-6">
                          <h4 className="text-lg font-medium text-white mb-2">
                            Key Responsibilities:
                          </h4>
                          <ul className="list-disc pl-5 space-y-1 text-gray-300">
                            {job.responsibilities.map((item, index) => (
                              <li key={index}>{item}</li>
                            ))}
                          </ul>
                        </div>
                        <div className="flex flex-wrap gap-2">
                          {job.skills.map((skill, index) => (
                            <Badge
                              key={index}
                              className="bg-gray-700 hover:bg-gray-600 text-gray-200"
                            >
                              {skill}
                            </Badge>
                          ))}
                        </div>
                      </CardContent>
                    </Card>
                  </motion.div>
                ))
              ) : (
                <div className="text-center py-12 text-gray-400">
                  <p className="text-xl">
                    No matching positions found. Try adjusting your search
                    criteria.
                  </p>
                </div>
              )}
            </motion.div>
          </div>
        </div>
      </section>

      {/* Benefits Section */}
      <section className="py-16 bg-black relative overflow-hidden">
        <div className="absolute inset-0 z-0">
          <div className="absolute w-full h-full bg-[radial-gradient(circle_at_20%_50%,rgba(64,25,109,0.3)_0%,transparent_50%)]"></div>
        </div>
        <div className="container mx-auto px-4 relative z-10">
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            viewport={{ once: true }}
            transition={{ duration: 0.6 }}
            className="max-w-4xl mx-auto mb-12 text-center"
          >
            <h2 className="text-3xl md:text-4xl font-bold mb-4 text-white">
              Why Join OfStartup?
            </h2>
            <p className="text-xl text-gray-400">
              We take care of our team so they can focus on what they do best
            </p>
          </motion.div>

          <motion.div
            className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6"
            variants={containerVariants}
            initial="hidden"
            whileInView="visible"
            viewport={{ once: true, amount: 0.3 }}
          >
            {benefits.map((benefit, index) => (
              <motion.div
                key={index}
                variants={itemVariants}
                className="relative group"
              >
                <div className="absolute -inset-0.5 bg-gradient-to-r from-blue-500 to-purple-600 rounded-lg blur opacity-0 group-hover:opacity-100 transition duration-1000 group-hover:duration-200"></div>
                <Card className="relative bg-gray-800 border-gray-700 h-full">
                  <CardHeader>
                    <div className="text-3xl mb-4">{benefit.icon}</div>
                    <CardTitle className="text-xl text-white">
                      {benefit.title}
                    </CardTitle>
                  </CardHeader>
                  <CardContent>
                    <p className="text-gray-300">{benefit.description}</p>
                  </CardContent>
                </Card>
              </motion.div>
            ))}
          </motion.div>
        </div>
      </section>

      {/* Testimonials & Culture */}
      <section className="py-16 bg-gradient-to-b from-gray-900 to-black">
        <div className="container mx-auto px-4">
          <Tabs defaultValue="testimonials" className="max-w-5xl mx-auto">
            <TabsList className="grid w-full grid-cols-2 mb-8 bg-gray-800">
              <TabsTrigger value="testimonials" className="text-lg">
                Employee Stories
              </TabsTrigger>
              <TabsTrigger value="culture" className="text-lg">
                Our Culture
              </TabsTrigger>
            </TabsList>

            <TabsContent value="testimonials">
              <motion.div
                className="grid grid-cols-1 md:grid-cols-3 gap-6"
                variants={containerVariants}
                initial="hidden"
                animate="visible"
              >
                {testimonials.map((testimonial, index) => (
                  <motion.div key={index} variants={itemVariants}>
                    <Card className="bg-gray-800 border-gray-700 h-full">
                      <CardHeader>
                        <div className="flex items-center gap-4">
                          <Image
                            fill
                            src={testimonial.image}
                            alt={testimonial.author}
                            className="w-12 h-12 rounded-full"
                          />
                          <div>
                            <CardTitle className="text-lg text-white">
                              {testimonial.author}
                            </CardTitle>
                            <CardDescription className="text-gray-400">
                              {testimonial.role}
                            </CardDescription>
                          </div>
                        </div>
                      </CardHeader>
                      <CardContent>
                        <p className="text-gray-300 italic mb-4">
                          &quot;{testimonial.quote}&quot;
                        </p>
                        <p className="text-gray-400 text-sm">
                          {testimonial.duration}
                        </p>
                      </CardContent>
                    </Card>
                  </motion.div>
                ))}
              </motion.div>
            </TabsContent>

            <TabsContent value="culture">
              <Card className="bg-gray-800 border-gray-700">
                <CardHeader>
                  <CardTitle className="text-2xl text-white">
                    Our Values & Culture
                  </CardTitle>
                  <CardDescription className="text-gray-400">
                    What makes OfStartup different
                  </CardDescription>
                </CardHeader>
                <CardContent>
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
                    <div>
                      <h3 className="text-xl font-medium text-white mb-4">
                        Our Values
                      </h3>
                      <ul className="space-y-4">
                        <li className="flex items-start gap-3">
                          <div className="mt-1 bg-blue-500/20 p-1 rounded-full">
                            <Check className="w-4 h-4 text-blue-500" />
                          </div>
                          <div>
                            <h4 className="font-medium text-white">
                              Innovation First
                            </h4>
                            <p className="text-gray-400">
                              We embrace new ideas and approaches, constantly
                              pushing the boundaries of what&apos;s possible.
                            </p>
                          </div>
                        </li>
                        <li className="flex items-start gap-3">
                          <div className="mt-1 bg-purple-500/20 p-1 rounded-full">
                            <Check className="w-4 h-4 text-purple-500" />
                          </div>
                          <div>
                            <h4 className="font-medium text-white">
                              Client Success
                            </h4>
                            <p className="text-gray-400">
                              We measure our success by the value we create for
                              our clients and their businesses.
                            </p>
                          </div>
                        </li>
                        <li className="flex items-start gap-3">
                          <div className="mt-1 bg-blue-500/20 p-1 rounded-full">
                            <Check className="w-4 h-4 text-blue-500" />
                          </div>
                          <div>
                            <h4 className="font-medium text-white">
                              Continuous Learning
                            </h4>
                            <p className="text-gray-400">
                              We foster a culture of growth, encouraging
                              everyone to expand their skills and knowledge.
                            </p>
                          </div>
                        </li>
                        <li className="flex items-start gap-3">
                          <div className="mt-1 bg-purple-500/20 p-1 rounded-full">
                            <Check className="w-4 h-4 text-purple-500" />
                          </div>
                          <div>
                            <h4 className="font-medium text-white">
                              Diversity & Inclusion
                            </h4>
                            <p className="text-gray-400">
                              We believe diverse perspectives lead to better
                              solutions and a stronger company.
                            </p>
                          </div>
                        </li>
                      </ul>
                    </div>
                    <div>
                      <h3 className="text-xl font-medium text-white mb-4">
                        Life at OfStartup
                      </h3>
                      <div className="space-y-4">
                        <div className="rounded-lg overflow-hidden">
                          <Image
                            fill
                            src="/api/placeholder/400/200"
                            alt="Team collaboration"
                            className="w-full h-auto"
                          />
                        </div>
                        <p className="text-gray-300">
                          At OfStartup, we foster a collaborative environment
                          where everyone&apos;s voice matters. We work hard but
                          also make time for team building, celebrations, and
                          personal growth.
                        </p>
                        <p className="text-gray-300">
                          Our offices are designed to encourage creativity and
                          collaboration, with flexible workspaces, relaxation
                          areas, and state-of-the-art technology at your
                          fingertips.
                        </p>
                        <Button className="mt-2 bg-gradient-to-r from-blue-600 to-purple-600 hover:from-blue-700 hover:to-purple-700 text-white">
                          View More Photos
                        </Button>
                      </div>
                    </div>
                  </div>
                </CardContent>
              </Card>
            </TabsContent>
          </Tabs>
        </div>
      </section>

      {/* FAQs Section */}
      <section className="py-16 bg-gray-900">
        <div className="container mx-auto px-4">
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            viewport={{ once: true }}
            transition={{ duration: 0.6 }}
            className="max-w-4xl mx-auto mb-12 text-center"
          >
            <h2 className="text-3xl md:text-4xl font-bold mb-4 text-white">
              Frequently Asked Questions
            </h2>
            <p className="text-xl text-gray-400">
              Everything you need to know about joining our team
            </p>
          </motion.div>

          <div className="max-w-3xl mx-auto">
            <Accordion type="single" collapsible className="w-full">
              {faqs.map((faq, index) => (
                <AccordionItem
                  key={index}
                  value={`item-${index}`}
                  className="border-gray-700"
                >
                  <AccordionTrigger className="text-white hover:text-blue-400 text-lg">
                    {faq.question}
                  </AccordionTrigger>
                  <AccordionContent className="text-gray-300">
                    {faq.answer}
                  </AccordionContent>
                </AccordionItem>
              ))}
            </Accordion>
          </div>
        </div>
      </section>

      {/* Application Process */}
      <section className="py-16 bg-black relative overflow-hidden">
        <div className="absolute inset-0 z-0">
          <div className="absolute w-full h-full bg-[radial-gradient(circle_at_70%_30%,rgba(64,25,109,0.3)_0%,transparent_50%)]"></div>
        </div>
        <div className="container mx-auto px-4 relative z-10">
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            viewport={{ once: true }}
            transition={{ duration: 0.6 }}
            className="max-w-4xl mx-auto mb-12 text-center"
          >
            <h2 className="text-3xl md:text-4xl font-bold mb-4 text-white">
              Our Hiring Process
            </h2>
            <p className="text-xl text-gray-400">
              What to expect when you apply at OfStartup
            </p>
          </motion.div>

          <div className="max-w-5xl mx-auto">
            <motion.div
              className="relative"
              variants={containerVariants}
              initial="hidden"
              whileInView="visible"
              viewport={{ once: true, amount: 0.3 }}
            >
              {/* Timeline line */}
              <div className="absolute h-full w-0.5 bg-gradient-to-b from-blue-500 via-purple-500 to-blue-500 left-0 md:left-1/2 transform md:-translate-x-1/2 top-0"></div>

              {/* Timeline items */}
              <div className="grid grid-cols-1 md:grid-cols-2 gap-8 md:gap-12">
                <motion.div
                  className="md:text-right md:pr-12 relative"
                  variants={itemVariants}
                >
                  <div className="hidden md:block absolute top-0 right-0 w-3 h-3 rounded-full bg-blue-500 transform translate-x-1.5"></div>
                  <h3 className="text-xl font-bold text-white mb-2">
                    Application Review
                  </h3>
                  <p className="text-gray-300">
                    Our recruitment team reviews your application, resume, and
                    portfolio to evaluate your fit for the role.
                  </p>
                  <p className="text-gray-400 mt-2">Timeline: 1-2 weeks</p>
                </motion.div>
                <motion.div
                  className="md:hidden"
                  variants={itemVariants}
                ></motion.div>

                <motion.div
                  className="md:hidden"
                  variants={itemVariants}
                ></motion.div>
                <motion.div
                  className="pl-12 md:pl-0 relative"
                  variants={itemVariants}
                >
                  <div className="absolute top-0 left-0 md:left-auto md:right-full w-3 h-3 rounded-full bg-purple-500 transform md:translate-x-1.5"></div>
                  <h3 className="text-xl font-bold text-white mb-2">
                    Initial Conversation
                  </h3>
                  <p className="text-gray-300">
                    A call with our recruiter to discuss your background, career
                    goals, and answer any questions you have about OfStartup.
                  </p>
                  <p className="text-gray-400 mt-2">Duration: 30-45 minutes</p>
                </motion.div>

                <motion.div
                  className="md:text-right md:pr-12 relative"
                  variants={itemVariants}
                >
                  <div className="hidden md:block absolute top-0 right-0 w-3 h-3 rounded-full bg-blue-500 transform translate-x-1.5"></div>
                  <h3 className="text-xl font-bold text-white mb-2">
                    Technical Assessment
                  </h3>
                  <p className="text-gray-300">
                    Depending on the role, you may complete a technical
                    challenge, case study, or portfolio review to showcase your
                    skills.
                  </p>
                  <p className="text-gray-400 mt-2">
                    Timeline: 3-5 days to complete
                  </p>
                </motion.div>
                <motion.div
                  className="md:hidden"
                  variants={itemVariants}
                ></motion.div>

                <motion.div
                  className="md:hidden"
                  variants={itemVariants}
                ></motion.div>
                <motion.div
                  className="pl-12 md:pl-0 relative"
                  variants={itemVariants}
                >
                  <div className="absolute top-0 left-0 md:left-auto md:right-full w-3 h-3 rounded-full bg-purple-500 transform md:translate-x-1.5"></div>
                  <h3 className="text-xl font-bold text-white mb-2">
                    Team Interview
                  </h3>
                  <p className="text-gray-300">
                    Meet with potential team members and peers to discuss your
                    experience and how you approach problems in your field.
                  </p>
                  <p className="text-gray-400 mt-2">Duration: 60-90 minutes</p>
                </motion.div>

                <motion.div
                  className="md:text-right md:pr-12 relative"
                  variants={itemVariants}
                >
                  <div className="hidden md:block absolute top-0 right-0 w-3 h-3 rounded-full bg-blue-500 transform translate-x-1.5"></div>
                  <h3 className="text-xl font-bold text-white mb-2">
                    Leadership Conversation
                  </h3>
                  <p className="text-gray-300">
                    Connect with department leaders to discuss your potential
                    impact and alignment with company vision and values.
                  </p>
                  <p className="text-gray-400 mt-2">Duration: 45-60 minutes</p>
                </motion.div>
                <motion.div
                  className="md:hidden"
                  variants={itemVariants}
                ></motion.div>

                <motion.div
                  className="md:hidden"
                  variants={itemVariants}
                ></motion.div>
                <motion.div
                  className="pl-12 md:pl-0 relative"
                  variants={itemVariants}
                >
                  <div className="absolute top-0 left-0 md:left-auto md:right-full w-3 h-3 rounded-full bg-purple-500 transform md:translate-x-1.5"></div>
                  <h3 className="text-xl font-bold text-white mb-2">
                    Offer & Onboarding
                  </h3>
                  <p className="text-gray-300">
                    If successful, you&apos;ll receive an offer and begin our
                    comprehensive onboarding program designed to set you up for
                    success.
                  </p>
                  <p className="text-gray-400 mt-2">Timeline: 2-4 weeks</p>
                </motion.div>
              </div>
            </motion.div>
          </div>
        </div>
      </section>

      {/* Contact & Apply CTA */}
      <section className="py-20 bg-gradient-to-b from-gray-900 to-black">
        <div className="container mx-auto px-4">
          <div className="max-w-4xl mx-auto">
            <motion.div
              initial={{ opacity: 0, scale: 0.9 }}
              whileInView={{ opacity: 1, scale: 1 }}
              viewport={{ once: true }}
              transition={{ duration: 0.6 }}
              className="bg-gradient-to-r from-blue-900/30 to-purple-900/30 backdrop-blur-sm rounded-xl p-8 md:p-12 border border-gray-800"
            >
              <div className="text-center mb-8">
                <h2 className="text-3xl md:text-4xl font-bold mb-4 text-white">
                  Ready to Join Our Team?
                </h2>
                <p className="text-xl text-gray-300">
                  We&apos;re always looking for talented individuals who are
                  passionate about technology and innovation.
                </p>
              </div>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
                <div>
                  <h3 className="text-xl font-bold text-white mb-4">
                    Apply Today
                  </h3>
                  <p className="text-gray-300 mb-6">
                    Check out our open positions above or send us your resume
                    for future opportunities.
                  </p>
                  <Button className="w-full bg-gradient-to-r from-blue-600 to-purple-600 hover:from-blue-700 hover:to-purple-700 text-white">
                    Browse Open Positions
                  </Button>
                </div>

                <div>
                  <h3 className="text-xl font-bold text-white mb-4">
                    Have Questions?
                  </h3>
                  <p className="text-gray-300 mb-6">
                    Reach out to our recruitment team for more information about
                    careers at OfStartup.
                  </p>
                  <Button
                    variant="outline"
                    className="w-full border-gray-600 text-gray-300 hover:bg-gray-800"
                  >
                    Contact Recruitment
                  </Button>
                </div>
              </div>
            </motion.div>
          </div>
        </div>
      </section>
    </div>
  );
};

export default CareersPage;
