"use client";

import { useRef, useState } from "react";
import { motion, AnimatePresence, useInView } from "framer-motion";
import { Tabs, Ta<PERSON>Content, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { But<PERSON> } from "@/components/ui/button";
import { Card, CardContent } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Separator } from "@/components/ui/separator";
import { ScrollArea, ScrollBar } from "@/components/ui/scroll-area";
import {
  ChevronRight,
  Sparkles,
  Building,
  Code,
  Database,
  CheckCircle2,
  BrainCircuit,
  Shield,
  Globe,
} from "lucide-react";
import Link from "next/link";

const solutions = [
  {
    id: "tech",
    title: "Tech Solutions",
    description:
      "Cutting-edge technology solutions built with the latest tech stacks to transform your digital presence and capabilities.",
    icon: Code,
    color: "blue",
    gradientFrom: "from-blue-600",
    gradientTo: "to-cyan-600",
    features: [
      "Full-Stack Development",
      "Modern Frontend Frameworks",
      "Scalable Backend Architecture",
      "Cloud-Native Solutions",
      "DevOps & CI/CD Implementation",
      "Mobile & Cross-Platform Apps",
    ],
    stat: { value: "99.9%", label: "Uptime for deployed solutions" },
  },
  {
    id: "ai",
    title: "AI & ML Solutions",
    description:
      "Harness the power of artificial intelligence and machine learning to gain insights, automate processes, and create intelligent products.",
    icon: BrainCircuit,
    color: "indigo",
    gradientFrom: "from-indigo-600",
    gradientTo: "to-violet-600",
    features: [
      "Custom AI Model Development",
      "NLP & Computer Vision Solutions",
      "Predictive Analytics",
      "ML Ops & Model Deployment",
      "Data Pipeline Automation",
      "AI Product Strategy",
    ],
    stat: { value: "3.5x", label: "Avg. efficiency improvement" },
  },
  {
    id: "business",
    title: "Business Solutions",
    description:
      "Strategic business solutions that help you plan, launch, and scale your vision in competitive markets.",
    icon: Building,
    color: "emerald",
    gradientFrom: "from-emerald-600",
    gradientTo: "to-teal-600",
    features: [
      "MVP Development & Validation",
      "Go-to-Market Strategy",
      "Digital Transformation",
      "Process Optimization",
      "Growth & Scaling Frameworks",
      "Business Intelligence",
    ],
    stat: { value: "87%", label: "Success rate for launched products" },
  },
  {
    id: "data",
    title: "Data Solutions",
    description:
      "Transform raw data into actionable intelligence with our comprehensive data solutions and analytics platforms.",
    icon: Database,
    color: "amber",
    gradientFrom: "from-amber-600",
    gradientTo: "to-orange-600",
    features: [
      "Big Data Engineering",
      "Data Warehousing",
      "Business Intelligence Dashboards",
      "ETL Pipeline Development",
      "Real-time Analytics",
      "Data Strategy Consulting",
    ],
    stat: { value: "2.8x", label: "Avg. ROI on data projects" },
  },
  {
    id: "web3",
    title: "Web3 & Blockchain",
    description:
      "Step into the future with decentralized solutions built on cutting-edge blockchain technology and Web3 infrastructure.",
    icon: Globe,
    color: "purple",
    gradientFrom: "from-purple-600",
    gradientTo: "to-pink-600",
    features: [
      "Smart Contract Development",
      "Decentralized Applications (dApps)",
      "Tokenization & NFT Platforms",
      "Blockchain Integration",
      "Web3 Frontend Development",
      "DAO Infrastructure",
    ],
    stat: { value: "78%", label: "Reduction in transaction costs" },
  },
  {
    id: "security",
    title: "Security Solutions",
    description:
      "Protect your digital assets with enterprise-grade security solutions designed to address modern threats.",
    icon: Shield,
    color: "red",
    gradientFrom: "from-red-600",
    gradientTo: "to-rose-600",
    features: [
      "Application Security Testing",
      "Penetration Testing",
      "Security Architecture Design",
      "Compliance & Audit Support",
      "DevSecOps Implementation",
      "Incident Response Planning",
    ],
    stat: { value: "94%", label: "Threat detection accuracy" },
  },
];

const FeatureCard = ({ feature, index, color }) => {
  const ref = useRef(null);
  const isInView = useInView(ref, { once: true, amount: 0.5 });

  return (
    <motion.div
      ref={ref}
      initial={{ opacity: 0, y: 20 }}
      animate={isInView ? { opacity: 1, y: 0 } : { opacity: 0, y: 20 }}
      transition={{ duration: 0.5, delay: index * 0.1 }}
      className={`group rounded-xl border border-${color}-500/20 bg-gradient-to-br from-background to-${color}-950/10 backdrop-blur-sm p-4 hover:shadow-lg hover:shadow-${color}-500/5 transition-all duration-300`}
    >
      <div className="flex items-start gap-3">
        <div
          className={`flex-shrink-0 mt-0.5 p-1.5 rounded-full bg-${color}-500/10 text-${color}-500`}
        >
          <CheckCircle2 className="h-4 w-4" />
        </div>
        <span className="text-sm font-medium">{feature}</span>
      </div>
    </motion.div>
  );
};

const SolutionsSection = () => {
  const [activeTab, setActiveTab] = useState("tech");
  const sectionRef = useRef(null);
  const isInView = useInView(sectionRef, { once: true, amount: 0.1 });

  return (
    <section
      ref={sectionRef}
      id="solutions"
      className="relative py-24 overflow-hidden"
    >
      {/* Top gradient blur fade */}
      <div className="absolute top-0 left-0 right-0 h-24 bg-gradient-to-b from-background via-background/90 to-transparent pointer-events-none z-10" />

      {/* Background elements */}
      <div className="absolute inset-0 bg-[radial-gradient(ellipse_at_top_right,var(--tw-gradient-stops))] from-slate-900/50 via-background to-background z-0" />

      {/* Animated particles */}
      <div className="absolute inset-0 overflow-hidden">
        {Array.from({ length: 20 }).map((_, i) => (
          <motion.div
            key={i}
            className={`absolute rounded-full ${
              i % 3 === 0
                ? "bg-blue-500"
                : i % 3 === 1
                ? "bg-indigo-500"
                : "bg-emerald-500"
            } opacity-10`}
            style={{
              width: Math.random() * 40 + 10 + "px",
              height: Math.random() * 40 + 10 + "px",
              left: Math.random() * 100 + "%",
              top: Math.random() * 100 + "%",
            }}
            animate={{
              y: [Math.random() * 100, Math.random() * -100],
              opacity: [0.05, 0.15, 0.05],
            }}
            transition={{
              duration: Math.random() * 20 + 15,
              repeat: Infinity,
              ease: "easeInOut",
            }}
          />
        ))}
      </div>

      {/* Mesh grid */}
      <div className="absolute inset-0 bg-[linear-gradient(to_right,#80808008_1px,transparent_1px),linear-gradient(to_bottom,#80808008_1px,transparent_1px)] bg-[size:40px_40px] z-0" />

      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 relative z-[1]">
        <motion.div
          initial={{ opacity: 0, y: -20 }}
          animate={isInView ? { opacity: 1, y: 0 } : { opacity: 0, y: -20 }}
          transition={{ duration: 0.6 }}
          className="text-center mb-16"
        >
          <Badge
            variant="outline"
            className="px-4 py-1.5 mb-6 bg-slate-900/50 text-primary border-primary/20 rounded-full backdrop-blur-sm"
          >
            End-to-End Solutions
          </Badge>
          <h2 className="text-4xl md:text-5xl font-bold mb-4 bg-clip-text text-transparent bg-gradient-to-r from-white to-gray-400">
            Technology & Business Solutions
          </h2>
          <p className="text-lg md:text-xl text-gray-400 max-w-3xl mx-auto">
            From planning and development to scaling and optimization, we
            provide comprehensive solutions using the latest technologies.
          </p>
        </motion.div>

        <Tabs
          defaultValue="tech"
          className="w-full"
          onValueChange={setActiveTab}
        >
          <div className="relative flex justify-center">
            <div className="absolute left-0 right-0 top-0 h-px bg-gradient-to-r from-transparent via-gray-700 to-transparent opacity-50" />

            <ScrollArea className="w-full pb-6">
              <div className="flex justify-center">
                <TabsList className="h-16 rounded-lg bg-slate-900/50 backdrop-blur-md border border-white/5 p-1.5">
                  {solutions.map((solution) => (
                    <TabsTrigger
                      key={solution.id}
                      value={solution.id}
                      className={`px-6 h-full text-base transition-all duration-300 data-[state=active]:${solution.gradientFrom} data-[state=active]:${solution.gradientTo} data-[state=active]:text-white data-[state=active]:shadow-lg rounded-md`}
                    >
                      <motion.div
                        className="flex items-center gap-2.5"
                        whileTap={{ scale: 0.97 }}
                      >
                        <solution.icon className="h-5 w-5" />
                        <span className="font-medium">{solution.title}</span>
                      </motion.div>
                    </TabsTrigger>
                  ))}
                </TabsList>
              </div>
              <ScrollBar orientation="horizontal" className="hidden" />
            </ScrollArea>

            <div className="absolute left-0 right-0 bottom-0 h-px bg-gradient-to-r from-transparent via-gray-700 to-transparent opacity-50" />
          </div>

          <div className="mt-12">
            <AnimatePresence mode="wait">
              {solutions.map(
                (solution) =>
                  solution.id === activeTab && (
                    <TabsContent
                      key={solution.id}
                      value={solution.id}
                      className="outline-none mt-0"
                    >
                      <motion.div
                        initial={{ opacity: 0, y: 20 }}
                        animate={{ opacity: 1, y: 0 }}
                        exit={{ opacity: 0, y: -20 }}
                        transition={{ duration: 0.5 }}
                      >
                        <Card className="bg-gradient-to-br from-slate-900/60 to-slate-950/80 backdrop-blur-lg border border-white/10 shadow-2xl rounded-2xl overflow-hidden">
                          <CardContent className="p-0">
                            {/* Gradient accent line at top */}
                            <div
                              className={`h-1.5 w-full bg-gradient-to-r ${solution.gradientFrom} ${solution.gradientTo}`}
                            />

                            <div className="p-8 md:p-10">
                              <div className="grid md:grid-cols-2 gap-12 items-start">
                                <div>
                                  <motion.div
                                    initial={{ opacity: 0, y: 20 }}
                                    animate={{ opacity: 1, y: 0 }}
                                    transition={{ duration: 0.6, delay: 0.1 }}
                                  >
                                    <div
                                      className={`inline-flex items-center justify-center h-14 w-14 rounded-xl bg-gradient-to-br ${solution.gradientFrom} ${solution.gradientTo} mb-6 shadow-lg shadow-${solution.color}-500/20`}
                                    >
                                      <solution.icon className="h-7 w-7 text-white" />
                                    </div>

                                    <h3
                                      className={`text-3xl font-bold mb-3 bg-clip-text text-transparent bg-gradient-to-r ${solution.gradientFrom} ${solution.gradientTo}`}
                                    >
                                      {solution.title}
                                    </h3>
                                    <p className="text-gray-400 mb-6 text-lg leading-relaxed">
                                      {solution.description}
                                    </p>

                                    <div className="flex flex-col sm:flex-row sm:items-center gap-3 sm:gap-6 mb-8">
                                      <div className="p-4 rounded-lg border border-white/10 bg-white/5 backdrop-blur-sm">
                                        <div
                                          className={`text-2xl font-bold bg-clip-text text-transparent bg-gradient-to-r ${solution.gradientFrom} ${solution.gradientTo}`}
                                        >
                                          {solution.stat.value}
                                        </div>
                                        <div className="text-xs text-gray-400 mt-1">
                                          {solution.stat.label}
                                        </div>
                                      </div>
                                      <Separator
                                        orientation="vertical"
                                        className="hidden sm:block h-12 bg-gray-800"
                                      />
                                      <Link href="/solutions">
                                        <Button
                                          size="lg"
                                          className={`bg-gradient-to-r ${solution.gradientFrom} ${solution.gradientTo} hover:opacity-90 text-white border-0 transition-all duration-300 group w-full sm:w-auto shadow-md shadow-${solution.color}-500/20`}
                                        >
                                          <span>Explore Solutions</span>
                                          <ChevronRight className="ml-2 h-4 w-4 transition-transform group-hover:translate-x-1" />
                                        </Button>
                                      </Link>
                                    </div>
                                  </motion.div>
                                </div>

                                <div className="space-y-6">
                                  <motion.div
                                    initial={{ opacity: 0, y: 20 }}
                                    animate={{ opacity: 1, y: 0 }}
                                    transition={{ duration: 0.6, delay: 0.2 }}
                                  >
                                    <h4 className="text-lg font-semibold mb-6 flex items-center">
                                      <span
                                        className={`inline-block h-1 w-6 mr-2 rounded-full bg-${solution.color}-500`}
                                      ></span>
                                      What We Deliver
                                    </h4>
                                    <div className="grid grid-cols-1 sm:grid-cols-2 gap-3">
                                      {solution.features.map(
                                        (feature, index) => (
                                          <FeatureCard
                                            key={index}
                                            feature={feature}
                                            index={index}
                                            color={solution.color}
                                          />
                                        )
                                      )}
                                    </div>
                                  </motion.div>
                                </div>
                              </div>
                            </div>
                          </CardContent>
                        </Card>
                      </motion.div>
                    </TabsContent>
                  )
              )}
            </AnimatePresence>
          </div>
        </Tabs>
      </div>

      {/* Bottom gradient blur fade */}
      <div className="absolute bottom-0 left-0 right-0 h-24 bg-gradient-to-t from-background via-background/90 to-transparent pointer-events-none z-10" />
    </section>
  );
};

export default SolutionsSection;
