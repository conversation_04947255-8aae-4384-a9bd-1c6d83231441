import "./globals.css";
import { Inter } from "next/font/google";
import { ThemeProvider } from "@/components/theme-provider";
import Navbar from "@/components/navbar";
import Footer from "@/components/footer";
import { Toaster } from "@/components/ui/sonner";

const inter = Inter({ subsets: ["latin"] });

export const metadata = {
  // Title: Reflects tech + business expertise (58 characters)
  title: "OfStartup | AI Tech & Business Growth Solutions",

  // Description: Blends tech and business value (149 characters)
  description:
    "Scale smarter with OfStartup’s AI tech, web, SaaS, and business consultancy. Boost revenue and transform startups & enterprises with expert solutions!",

  // Keywords: Expanded for business consultancy and revenue focus
  keywords:
    "AI tech solutions, business consultancy for startups, web development agency, custom software experts, SaaS development, digital transformation, revenue growth solutions, machine learning services, startup scaling agency, enterprise business consulting, cloud tech innovations",

  // Open Graph (OG) Tags: Highlight dual expertise
  openGraph: {
    title: "OfStartup | AI Tech & Business Growth Consultancy",
    description:
      "Grow faster with OfStartup’s AI-driven tech and expert business consultancy. Web, software, SaaS, and revenue-boosting strategies for startups & enterprises.",
    url: "https://www.ofstartup.com",
    type: "website",
    locale: "en_US",
    site_name: "OfStartup",
    images: [
      {
        url: "https://www.ofstartup.com/assets/og-image-1200x630.jpg",
        width: 1200,
        height: 630,
        alt: "OfStartup - AI Tech & Business Growth Solutions",
      },
    ],
  },

  // Twitter Card: Emphasize tech + business impact
  twitter: {
    card: "summary_large_image",
    site: "@OfStartupTech", // Replace with your Twitter handle
    creator: "@OfStartupTech",
    title: "OfStartup | AI Tech & Business Scaling Solutions",
    description:
      "AI-powered tech and business consultancy to skyrocket startup and enterprise growth. Boost revenue with custom web, software, and SaaS solutions!",
    image: "https://www.ofstartup.com/assets/twitter-card-800x418.jpg",
  },

  // Canonical URL
  alternates: {
    canonical: "https://www.ofstartup.com",
  },

  // Verification
  verification: {
    google: "your-google-verification-code", // Add your code
  },

  // Schema Markup: Updated to reflect tech + business services
  schema: {
    "@context": "https://schema.org",
    "@type": "Organization",
    name: "OfStartup",
    url: "https://www.ofstartup.com",
    logo: "https://www.ofstartup.com/assets/logo.png",
    description:
      "OfStartup blends AI-powered tech solutions and business consultancy to help startups and enterprises scale, boost revenue, and achieve digital excellence.",
    sameAs: [
      "https://www.linkedin.com/company/ofstartup",
      "https://twitter.com/OfStartupTech",
      "https://www.facebook.com/OfStartup",
    ],
    contactPoint: {
      "@type": "ContactPoint",
      telephone: "******-555-1234", // Replace with your contact
      contactType: "Customer Service",
      email: "<EMAIL>",
    },
    service: [
      {
        "@type": "Service",
        serviceType: "Business Consultancy",
        description:
          "Strategic consulting to scale startups and enterprises, optimize operations, and boost revenue.",
      },
      {
        "@type": "Service",
        serviceType: "AI & Tech Solutions",
        description:
          "AI-driven web development, custom software, SaaS, and digital transformation services.",
      },
    ],
  },
};

export default function RootLayout({ children }) {
  return (
    <html lang="en" suppressHydrationWarning>
      <head>
        {/* Core Meta Tags */}
        <meta name="viewport" content="width=device-width, initial-scale=1.0" />
        <meta name="robots" content="index, follow, max-image-preview:large" />
        <meta name="author" content="OfStartup Team" />
        <meta charSet="UTF-8" />

        {/* Schema Markup Injection */}
        <script
          type="application/ld+json"
          dangerouslySetInnerHTML={{ __html: JSON.stringify(metadata.schema) }}
        />

        {/* Favicon and Performance */}
        <link rel="icon" href="/favicon.ico" sizes="any" />
        <link rel="preload" href="/assets/hero-image.jpg" as="image" />
      </head>
      <body className={`${inter.className} dark`}>
        <ThemeProvider
          attribute="class"
          defaultTheme="dark"
          enableSystem={false}
          disableTransitionOnChange
        >
          <Navbar />
          {children}
          <Footer />
          <Toaster />
        </ThemeProvider>
      </body>
    </html>
  );
}
