"use client";

import React from "react";
import { motion } from "framer-motion";
import { <PERSON>, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Tabs, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>ist, TabsTrigger } from "@/components/ui/tabs";
import Link from "next/link";
import { 
  ArrowRight, 
  Brain, 
  Code, 
  LineChart, 
  Lightbulb, 
  Rocket, 
  Users, 
  CheckCircle2,
  Zap,
  Target,
  Shield,
  Globe
} from "lucide-react";

const fadeIn = {
  hidden: { opacity: 0, y: 20 },
  visible: {
    opacity: 1,
    y: 0,
    transition: { duration: 0.6 },
  },
};

const staggerContainer = {
  hidden: { opacity: 0 },
  visible: {
    opacity: 1,
    transition: {
      staggerChildren: 0.2,
    },
  },
};

export default function ServicesPage() {
  const services = [
    {
      icon: Brain,
      title: "AI & Machine Learning Solutions",
      description: "Transform your business with cutting-edge artificial intelligence and machine learning technologies that automate processes and unlock valuable insights.",
      longDescription: "Our AI and ML experts design custom solutions that turn your data into competitive advantages. From predictive analytics to natural language processing, we help you harness the full power of artificial intelligence to drive growth and efficiency.",
      benefits: [
        "Custom AI model development and deployment",
        "Machine learning algorithm optimization",
        "Natural language processing solutions",
        "Computer vision and image recognition",
        "Predictive analytics and forecasting",
        "AI integration with existing systems"
      ],
      pricing: "Starting from $15,000/month",
    },
    {
      icon: Lightbulb,
      title: "Business Strategy & Consulting",
      description: "Develop comprehensive business strategies, market analysis, and growth plans tailored to your unique goals and industry challenges.",
      longDescription: "Our strategic consultants work closely with your leadership team to understand your market position, competitive landscape, and growth objectives. We deliver actionable insights and implementation roadmaps that drive measurable business results.",
      benefits: [
        "Comprehensive market analysis and research",
        "Strategic business planning and roadmaps",
        "Growth strategy and expansion planning",
        "Revenue optimization strategies",
        "Digital transformation consulting",
        "Executive coaching and leadership development"
      ],
      pricing: "Starting from $8,000/month",
    },
    {
      icon: Code,
      title: "Custom Software Development",
      description: "Build scalable, secure, and innovative software solutions tailored to your specific business requirements and technical needs.",
      longDescription: "Our development team creates custom software applications that perfectly align with your business processes. From web applications to mobile apps and enterprise systems, we deliver solutions that scale with your growth.",
      benefits: [
        "Full-stack web application development",
        "Mobile app development (iOS & Android)",
        "Enterprise software solutions",
        "API development and integration",
        "Cloud-native application architecture",
        "Ongoing maintenance and support"
      ],
      pricing: "Starting from $12,000/month",
    },
    {
      icon: LineChart,
      title: "Data Analytics & Business Intelligence",
      description: "Transform raw data into actionable insights with advanced analytics, visualization, and business intelligence solutions.",
      longDescription: "Our data scientists and analysts help you make sense of your data through sophisticated analytics platforms, custom dashboards, and predictive modeling that inform strategic decision-making.",
      benefits: [
        "Advanced data analytics and modeling",
        "Custom dashboard and visualization",
        "Business intelligence platform setup",
        "Data warehouse design and implementation",
        "Real-time reporting and monitoring",
        "Data-driven decision support systems"
      ],
      pricing: "Starting from $10,000/month",
    },
    {
      icon: Rocket,
      title: "Digital Transformation",
      description: "Modernize your business operations with comprehensive digital transformation strategies that improve efficiency and customer experience.",
      longDescription: "We guide organizations through complete digital transformation journeys, from legacy system modernization to implementing cutting-edge technologies that position you for future growth.",
      benefits: [
        "Legacy system modernization",
        "Cloud migration and optimization",
        "Process automation and optimization",
        "Digital workflow implementation",
        "Change management and training",
        "Technology stack optimization"
      ],
      pricing: "Starting from $20,000/month",
    },
    {
      icon: Users,
      title: "Team Augmentation",
      description: "Scale your development capabilities with our expert professionals who integrate seamlessly with your existing teams.",
      longDescription: "Access top-tier talent without the overhead of full-time hiring. Our professionals work as an extension of your team, bringing specialized skills and experience to accelerate your projects.",
      benefits: [
        "Skilled developers and specialists",
        "Flexible engagement models",
        "Quick onboarding and integration",
        "Reduced hiring and training costs",
        "Access to latest technologies",
        "Scalable team size based on needs"
      ],
      pricing: "Starting from $5,000/month per specialist",
    },
  ];

  const comparisonData = [
    {
      feature: "AI Integration",
      traditional: "Limited or no AI capabilities",
      ofstartup: "Advanced AI across all solutions",
    },
    {
      feature: "Cost Efficiency",
      traditional: "High operational costs",
      ofstartup: "25% average cost reduction",
    },
    {
      feature: "Implementation Time",
      traditional: "6-12 months typical",
      ofstartup: "2-4 months with AI acceleration",
    },
    {
      feature: "Scalability",
      traditional: "Manual scaling required",
      ofstartup: "Automatic AI-powered scaling",
    },
    {
      feature: "Support",
      traditional: "Business hours only",
      ofstartup: "24/7 AI-powered support",
    },
    {
      feature: "Customization",
      traditional: "Limited customization",
      ofstartup: "Fully customizable AI solutions",
    },
  ];

  return (
    <div className="min-h-screen bg-background">
      {/* Hero Section */}
      <section className="relative py-24 overflow-hidden">
        <div className="absolute inset-0 bg-gradient-to-br from-brand-50 to-brand-100 dark:from-gray-900 dark:to-gray-800" />
        <div className="container mx-auto px-6 relative z-10">
          <motion.div
            initial="hidden"
            animate="visible"
            variants={fadeIn}
            className="text-center max-w-4xl mx-auto"
          >
            <h1 className="text-5xl md:text-6xl font-heading font-bold mb-6 bg-gradient-to-r from-brand-600 to-brand-800 bg-clip-text text-transparent">
              Our Services
            </h1>
            <p className="text-xl text-muted-foreground mb-8 leading-relaxed">
              Comprehensive AI-powered solutions designed to transform your business operations, 
              reduce costs by 25%, and accelerate growth through intelligent automation and data-driven insights.
            </p>
            <div className="flex flex-col sm:flex-row gap-4 justify-center">
              <Button size="lg" asChild>
                <Link href="#services">
                  Explore Services <ArrowRight className="ml-2 h-4 w-4" />
                </Link>
              </Button>
              <Button variant="outline" size="lg" asChild>
                <Link href="/contact">Get Custom Quote</Link>
              </Button>
            </div>
          </motion.div>
        </div>
      </section>

      {/* Services Overview */}
      <section className="py-20">
        <div className="container mx-auto px-6">
          <motion.div
            initial="hidden"
            whileInView="visible"
            viewport={{ once: true }}
            variants={fadeIn}
            className="text-center max-w-3xl mx-auto mb-16"
          >
            <h2 className="text-3xl font-heading font-bold mb-6">Why Choose Ofstartup.ai?</h2>
            <p className="text-lg text-muted-foreground">
              We bring the best software and tools with AI to meet all your needs, seamlessly integrated into your company. 
              Our comprehensive approach ensures maximum value and minimal disruption.
            </p>
          </motion.div>

          <motion.div
            initial="hidden"
            whileInView="visible"
            viewport={{ once: true }}
            variants={staggerContainer}
            className="grid md:grid-cols-2 lg:grid-cols-4 gap-8 mb-16"
          >
            {[
              { icon: Target, title: "25% Cost Reduction", desc: "Proven savings across all implementations" },
              { icon: Zap, title: "AI-First Approach", desc: "Every solution powered by advanced AI" },
              { icon: Shield, title: "Enterprise Security", desc: "Bank-level security and compliance" },
              { icon: Globe, title: "Global Support", desc: "24/7 support across all time zones" },
            ].map((item, index) => (
              <motion.div key={index} variants={fadeIn}>
                <Card className="text-center h-full hover:shadow-lg transition-shadow">
                  <CardHeader>
                    <div className="mx-auto mb-4 p-3 bg-brand-100 dark:bg-brand-900 rounded-full w-fit">
                      <item.icon className="h-6 w-6 text-brand-600" />
                    </div>
                    <CardTitle className="text-lg">{item.title}</CardTitle>
                  </CardHeader>
                  <CardContent>
                    <p className="text-muted-foreground text-sm">{item.desc}</p>
                  </CardContent>
                </Card>
              </motion.div>
            ))}
          </motion.div>
        </div>
      </section>

      {/* Detailed Services Section */}
      <section id="services" className="py-20 bg-muted/50">
        <div className="container mx-auto px-6">
          <motion.div
            initial="hidden"
            whileInView="visible"
            viewport={{ once: true }}
            variants={fadeIn}
            className="text-center max-w-3xl mx-auto mb-16"
          >
            <h2 className="text-3xl font-heading font-bold mb-6">Our Service Portfolio</h2>
            <p className="text-lg text-muted-foreground">
              Comprehensive solutions designed to address every aspect of your digital transformation journey.
            </p>
          </motion.div>

          <motion.div
            initial="hidden"
            whileInView="visible"
            viewport={{ once: true }}
            variants={staggerContainer}
            className="grid lg:grid-cols-2 gap-8"
          >
            {services.map((service, index) => (
              <motion.div key={index} variants={fadeIn}>
                <Card className="h-full hover:shadow-lg transition-shadow">
                  <CardHeader>
                    <div className="flex items-center gap-3 mb-4">
                      <div className="p-2 bg-brand-100 dark:bg-brand-900 rounded-lg">
                        <service.icon className="h-6 w-6 text-brand-600" />
                      </div>
                      <div>
                        <CardTitle className="text-xl">{service.title}</CardTitle>
                        <CardDescription className="text-brand-600 font-medium">
                          {service.pricing}
                        </CardDescription>
                      </div>
                    </div>
                    <p className="text-muted-foreground">{service.description}</p>
                  </CardHeader>
                  <CardContent className="space-y-4">
                    <p className="text-sm leading-relaxed">{service.longDescription}</p>
                    <div>
                      <h4 className="font-semibold mb-2">Key Benefits:</h4>
                      <ul className="space-y-1">
                        {service.benefits.slice(0, 3).map((benefit, idx) => (
                          <li key={idx} className="flex items-center gap-2 text-sm">
                            <CheckCircle2 className="h-4 w-4 text-brand-600 flex-shrink-0" />
                            <span>{benefit}</span>
                          </li>
                        ))}
                      </ul>
                    </div>
                    <Button variant="outline" size="sm" asChild>
                      <Link href="/contact">
                        Learn More <ArrowRight className="ml-2 h-3 w-3" />
                      </Link>
                    </Button>
                  </CardContent>
                </Card>
              </motion.div>
            ))}
          </motion.div>
        </div>
      </section>

      {/* Comparison Section */}
      <section className="py-20">
        <div className="container mx-auto px-6">
          <motion.div
            initial="hidden"
            whileInView="visible"
            viewport={{ once: true }}
            variants={fadeIn}
            className="text-center max-w-3xl mx-auto mb-16"
          >
            <h2 className="text-3xl font-heading font-bold mb-6">Why Choose Us Over Traditional Solutions?</h2>
            <p className="text-lg text-muted-foreground">
              See how our AI-powered approach delivers superior results compared to traditional service providers.
            </p>
          </motion.div>

          <motion.div
            initial="hidden"
            whileInView="visible"
            viewport={{ once: true }}
            variants={fadeIn}
            className="max-w-4xl mx-auto"
          >
            <Card>
              <CardHeader>
                <CardTitle>Feature Comparison</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="overflow-x-auto">
                  <table className="w-full">
                    <thead>
                      <tr className="border-b">
                        <th className="text-left py-3 px-4">Feature</th>
                        <th className="text-left py-3 px-4">Traditional Solutions</th>
                        <th className="text-left py-3 px-4 text-brand-600">Ofstartup.ai</th>
                      </tr>
                    </thead>
                    <tbody>
                      {comparisonData.map((row, index) => (
                        <tr key={index} className="border-b">
                          <td className="py-3 px-4 font-medium">{row.feature}</td>
                          <td className="py-3 px-4 text-muted-foreground">{row.traditional}</td>
                          <td className="py-3 px-4 text-brand-600 font-medium">{row.ofstartup}</td>
                        </tr>
                      ))}
                    </tbody>
                  </table>
                </div>
              </CardContent>
            </Card>
          </motion.div>
        </div>
      </section>

      {/* CTA Section */}
      <section className="py-20 bg-gradient-to-r from-brand-600 to-brand-700 text-white">
        <div className="container mx-auto px-6">
          <motion.div
            initial="hidden"
            whileInView="visible"
            viewport={{ once: true }}
            variants={fadeIn}
            className="text-center max-w-3xl mx-auto"
          >
            <h2 className="text-3xl font-heading font-bold mb-6">
              Ready to Transform Your Business?
            </h2>
            <p className="text-xl mb-8 opacity-90">
              Join hundreds of companies that have reduced costs by 25% and accelerated growth with our AI-powered solutions.
            </p>
            <div className="flex flex-col sm:flex-row gap-4 justify-center">
              <Button size="lg" variant="secondary" asChild>
                <Link href="/contact">
                  Get Started Today <ArrowRight className="ml-2 h-4 w-4" />
                </Link>
              </Button>
              <Button size="lg" variant="outline" className="border-white text-white hover:bg-white hover:text-brand-600" asChild>
                <Link href="/about">Learn About Our Team</Link>
              </Button>
            </div>
          </motion.div>
        </div>
      </section>
    </div>
  );
}
