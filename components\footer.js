"use client";

import { useState } from "react";
import Link from "next/link";
import { motion } from "framer-motion";
import { Separator } from "@/components/ui/separator";
import { Input } from "@/components/ui/input";
import { Button } from "@/components/ui/button";
import { Zap, Mail, ArrowRight } from "lucide-react";

const Footer = () => {
  const [email, setEmail] = useState("");

  const containerVariants = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        staggerChildren: 0.1,
        delayChildren: 0.3,
      },
    },
  };

  const itemVariants = {
    hidden: { y: 20, opacity: 0 },
    visible: {
      y: 0,
      opacity: 1,
      transition: { type: "spring", stiffness: 100 },
    },
  };

  const handleSubscribe = (e) => {
    e.preventDefault();
    // Handle subscription logic
    setEmail("");
  };

  return (
    <footer className="border-t border-border pt-16 pb-8 overflow-hidden">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <motion.div
          variants={containerVariants}
          initial="hidden"
          whileInView="visible"
          viewport={{ once: true, amount: 0.3 }}
          className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-x-12 gap-y-10"
        >
          {/* Company Info */}
          <motion.div
            variants={itemVariants}
            className="col-span-1 md:col-span-2 lg:col-span-1"
          >
            <Link href="/" className="flex items-center space-x-2 mb-6">
              <motion.div
                whileHover={{ rotate: 360, scale: 1.1 }}
                transition={{ type: "spring", duration: 0.8 }}
              ></motion.div>
              <span className="text-2xl font-extrabold bg-gradient-to-r from-purple-500 to-blue-500 text-transparent bg-clip-text">
                OfStartup
              </span>
            </Link>
            <p className="text-gray-400 text-sm leading-relaxed mb-6 max-w-md">
              Transforming businesses with cutting-edge solutions and strategic
              expertise. We help startups and enterprises thrive in the digital
              age.
            </p>
          </motion.div>

          {/* Services */}
          <motion.div variants={itemVariants}>
            <h3 className="text-lg font-semibold mb-4 text-gray-200">
              Services
            </h3>
            <ul className="space-y-3">
              {[
                "Business Strategy",
                "AI & ML Solutions",
                "Web Development",
                "Software Development",
                "Data Analytics",
              ].map((service, index) => (
                <motion.li
                  key={index}
                  whileHover={{ x: 5 }}
                  transition={{ type: "spring", stiffness: 400 }}
                >
                  <Link
                    href="/services"
                    className="text-gray-400 hover:text-white transition-colors flex items-center gap-1 group"
                  >
                    <span>{service}</span>
                    <ArrowRight className="h-3 w-0 group-hover:w-3 transition-all duration-300 opacity-0 group-hover:opacity-100" />
                  </Link>
                </motion.li>
              ))}
            </ul>
          </motion.div>

          {/* Company */}
          <motion.div variants={itemVariants}>
            <h3 className="text-lg font-semibold mb-4 text-gray-200">
              Company
            </h3>
            <ul className="space-y-3">
              <motion.li
                whileHover={{ x: 5 }}
                transition={{ type: "spring", stiffness: 400 }}
              >
                <Link
                  href="/about"
                  className="text-gray-400 hover:text-white transition-colors flex items-center gap-1 group"
                >
                  <span>About Us</span>
                  <ArrowRight className="h-3 w-0 group-hover:w-3 transition-all duration-300 opacity-0 group-hover:opacity-100" />
                </Link>
              </motion.li>
              <motion.li
                whileHover={{ x: 5 }}
                transition={{ type: "spring", stiffness: 400 }}
              >
                <Link
                  href="/careers"
                  className="text-gray-400 hover:text-white transition-colors flex items-center gap-1 group"
                >
                  <span>Careers</span>
                  <ArrowRight className="h-3 w-0 group-hover:w-3 transition-all duration-300 opacity-0 group-hover:opacity-100" />
                </Link>
              </motion.li>
              <motion.li
                whileHover={{ x: 5 }}
                transition={{ type: "spring", stiffness: 400 }}
              >
                <Link
                  href="/blog"
                  className="text-gray-400 hover:text-white transition-colors flex items-center gap-1 group"
                >
                  <span>Blog</span>
                  <ArrowRight className="h-3 w-0 group-hover:w-3 transition-all duration-300 opacity-0 group-hover:opacity-100" />
                </Link>
              </motion.li>
              <motion.li
                whileHover={{ x: 5 }}
                transition={{ type: "spring", stiffness: 400 }}
              >
                <Link
                  href="/contact"
                  className="text-gray-400 hover:text-white transition-colors flex items-center gap-1 group"
                >
                  <span>Contact</span>
                  <ArrowRight className="h-3 w-0 group-hover:w-3 transition-all duration-300 opacity-0 group-hover:opacity-100" />
                </Link>
              </motion.li>
            </ul>
          </motion.div>

          {/* Legal */}
          <motion.div variants={itemVariants}>
            <h3 className="text-lg font-semibold mb-4 text-gray-200">Legal</h3>
            <ul className="space-y-3">
              {["Privacy Policy", "Terms of Service"].map((item, index) => (
                <motion.li
                  key={index}
                  whileHover={{ x: 5 }}
                  transition={{ type: "spring", stiffness: 400 }}
                >
                  <Link
                    href="#"
                    className="text-gray-400 hover:text-white transition-colors flex items-center gap-1 group"
                  >
                    <span>{item}</span>
                    <ArrowRight className="h-3 w-0 group-hover:w-3 transition-all duration-300 opacity-0 group-hover:opacity-100" />
                  </Link>
                </motion.li>
              ))}
            </ul>
          </motion.div>
        </motion.div>

        <Separator className="my-8 bg-slate-800" />

        <motion.div
          initial={{ opacity: 0, y: 20 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.5 }}
          viewport={{ once: true }}
          className="flex flex-col md:flex-row justify-between items-center"
        >
          <p className="text-gray-400 text-sm">
            &copy; {new Date().getFullYear()} OfStartup. All rights reserved.
          </p>

          <div className="flex items-center gap-6 mt-6 md:mt-0">
            <p className="text-gray-400 text-sm hidden sm:block">
              Connect with us
            </p>
          </div>
        </motion.div>
      </div>
    </footer>
  );
};

export default Footer;
