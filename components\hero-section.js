"use client";

import { useState, useEffect } from "react";
import { motion } from "framer-motion";
import { Button } from "@/components/ui/button";
import { ArrowRight, CheckCircle, Sparkles } from "lucide-react";
import { Badge } from "@/components/ui/badge";
import Link from "next/link";

const HeroSection = () => {
  const [isVisible, setIsVisible] = useState(false);

  useEffect(() => {
    setIsVisible(true);
  }, []);

  const fadeIn = {
    hidden: { opacity: 0, y: 20 },
    visible: (custom) => ({
      opacity: 1,
      y: 0,
      transition: {
        delay: custom * 0.1,
        duration: 0.6,
        ease: "easeOut",
      },
    }),
  };

  const features = [
    "Strategic Planning",
    "AI Integration/Automation",
    "Custom Development",
    "Digital Transformation",
  ];

  return (
    <div className="relative overflow-hidden pt-32 pb-32 md:pt-40 md:pb-40">
      {/* Dark background base */}
      <div className="absolute inset-0 bg-black z-0" />

      {/* Milky Way galaxy effect with multiple layers */}
      <div className="absolute inset-0 z-1 opacity-70">
        {/* Core galactic center - bright and dense */}
        <motion.div
          initial={{ opacity: 0 }}
          animate={{
            opacity: [0.6, 0.8, 0.6],
          }}
          transition={{
            duration: 8,
            repeat: Infinity,
            repeatType: "reverse",
          }}
          className="absolute top-1/2 left-1/2 -translate-x-1/2 -translate-y-1/2 w-[600px] h-[300px] rounded-full bg-gradient-to-r from-purple-900/40 via-indigo-400/30 to-purple-900/40 blur-3xl transform rotate-12"
        />

        {/* Milky Way spiral arm 1 */}
        <motion.div
          initial={{ opacity: 0, rotate: 0 }}
          animate={{
            opacity: 0.5,
            rotate: 360,
          }}
          transition={{
            opacity: {
              duration: 5,
              repeat: Infinity,
              repeatType: "reverse",
            },
            rotate: {
              duration: 240,
              repeat: Infinity,
              ease: "linear",
            },
          }}
          className="absolute top-1/2 left-1/2 -translate-x-1/2 -translate-y-1/2 w-[900px] h-[400px] rounded-full border-8 border-t-purple-500/10 border-l-blue-400/10 border-r-indigo-300/5 border-b-transparent blur-md"
        />

        {/* Milky Way spiral arm 2 */}
        <motion.div
          initial={{ opacity: 0, rotate: 90 }}
          animate={{
            opacity: 0.5,
            rotate: 450,
          }}
          transition={{
            opacity: {
              duration: 7,
              repeat: Infinity,
              repeatType: "reverse",
            },
            rotate: {
              duration: 280,
              repeat: Infinity,
              ease: "linear",
            },
          }}
          className="absolute top-1/2 left-1/2 -translate-x-1/2 -translate-y-1/2 w-[1000px] h-[420px] rounded-full border-8 border-t-indigo-300/10 border-r-purple-400/10 border-l-blue-500/5 border-b-transparent blur-md"
        />

        {/* Dust lanes */}
        <div className="absolute inset-0 bg-gradient-to-r from-transparent via-purple-900/10 to-transparent transform rotate-45 blur-xl"></div>
        <div className="absolute inset-0 bg-gradient-to-r from-transparent via-indigo-900/10 to-transparent transform -rotate-45 blur-xl"></div>
      </div>

      {/* Distant stars field - small dots */}
      <div className="absolute inset-0 z-1">
        {[...Array(100)].map((_, i) => (
          <motion.div
            key={i}
            className="absolute w-1 h-1 bg-white rounded-full"
            style={{
              top: `${Math.random() * 100}%`,
              left: `${Math.random() * 100}%`,
              opacity: Math.random() * 0.7 + 0.3,
            }}
            animate={{
              opacity: [0.3, 0.8, 0.3],
              scale: [1, 1.2, 1],
            }}
            transition={{
              duration: 2 + Math.random() * 3,
              repeat: Infinity,
              repeatType: "reverse",
              delay: Math.random() * 2,
            }}
          />
        ))}
      </div>

      {/* Brighter stars - larger points with glow */}
      <div className="absolute inset-0 z-1">
        {[...Array(15)].map((_, i) => (
          <motion.div
            key={i}
            className="absolute bg-white rounded-full shadow-glow"
            style={{
              top: `${Math.random() * 100}%`,
              left: `${Math.random() * 100}%`,
              width: `${Math.random() * 2 + 1}px`,
              height: `${Math.random() * 2 + 1}px`,
              boxShadow: `0 0 ${Math.random() * 4 + 2}px ${
                Math.random() * 2 + 1
              }px rgba(255, 255, 255, 0.8)`,
            }}
            animate={{
              opacity: [0.7, 1, 0.7],
              scale: [1, 1.3, 1],
            }}
            transition={{
              duration: 3 + Math.random() * 4,
              repeat: Infinity,
              repeatType: "reverse",
              delay: Math.random() * 2,
            }}
          />
        ))}
      </div>

      {/* Top right glare/lens flare */}
      <motion.div
        initial={{ opacity: 0 }}
        animate={{
          opacity: [0.5, 0.7, 0.5],
          scale: [1, 1.05, 1],
        }}
        transition={{
          duration: 5,
          repeat: Infinity,
          repeatType: "reverse",
        }}
        className="absolute top-0 right-0 w-[400px] h-[400px] bg-gradient-to-bl from-blue-400/30 via-purple-300/20 to-transparent blur-3xl"
      />

      {/* Bottom blend gradient */}
      <div className="absolute bottom-0 left-0 right-0 h-40 bg-gradient-to-t from-background to-transparent z-2" />

      {/* Content overlay with slight backdrop filter */}
      <div className="relative z-10 max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="text-center">
          <motion.div
            initial="hidden"
            animate={isVisible ? "visible" : "hidden"}
            custom={0}
            variants={fadeIn}
            className="inline-flex items-center rounded-full px-3 py-1 text-sm border border-purple-500/30 bg-purple-900/20 text-purple-200 mb-6 backdrop-blur-md"
          >
            <motion.div
              animate={{
                rotate: [0, 360],
              }}
              transition={{
                duration: 8,
                repeat: Infinity,
                ease: "linear",
              }}
              className="mr-2"
            >
              <Sparkles className="h-4 w-4 text-purple-300" />
            </motion.div>
            <Badge
              variant="outline"
              className="bg-purple-950/60 text-purple-200 border-purple-500/30 backdrop-blur-md"
            >
              Innovate Beyond Boundaries
            </Badge>
          </motion.div>

          <motion.h1
            initial="hidden"
            animate={isVisible ? "visible" : "hidden"}
            custom={1}
            variants={fadeIn}
            className="text-4xl md:text-5xl lg:text-6xl font-bold tracking-tight"
          >
            <span className="block text-white">Propel Your Business</span>
            <motion.span
              className="block mt-2 bg-gradient-to-r from-blue-400 via-purple-400 to-pink-400 text-transparent bg-clip-text"
              animate={{
                backgroundPosition: ["0% 50%", "100% 50%", "0% 50%"],
              }}
              transition={{
                duration: 15,
                repeat: Infinity,
                repeatType: "mirror",
              }}
            >
              Across The Digital Universe
            </motion.span>
          </motion.h1>

          <motion.p
            initial="hidden"
            animate={isVisible ? "visible" : "hidden"}
            custom={2}
            variants={fadeIn}
            className="mt-6 max-w-2xl mx-auto text-xl text-gray-300 backdrop-blur-sm"
          >
            Navigate the vast digital cosmos with our cutting-edge solutions
            that transform business challenges into stellar opportunities for
            innovation and growth.
          </motion.p>

          <motion.div
            initial="hidden"
            animate={isVisible ? "visible" : "hidden"}
            custom={3}
            variants={fadeIn}
            className="mt-8 flex flex-col sm:flex-row justify-center gap-4"
          >
            <Button
              className="bg-gradient-to-r from-blue-600 via-purple-600 to-pink-600 hover:from-blue-700 hover:via-purple-700 hover:to-pink-700 text-white px-6 py-6 rounded-lg font-medium group shadow-lg shadow-purple-900/30"
              size="lg"
            >
              <span>Launch Your Project</span>
              <motion.div
                animate={{ x: [0, 4, 0] }}
                transition={{
                  duration: 1.5,
                  repeat: Infinity,
                  repeatType: "loop",
                  ease: "easeInOut",
                  repeatDelay: 1,
                }}
              >
                <ArrowRight className="ml-2 h-4 w-4" />
              </motion.div>
            </Button>

            <Link href="/services">
              <Button
                variant="outline"
                size="lg"
                className="border-purple-500/30 text-gray-200 hover:text-white hover:bg-purple-900/20 px-6 py-6 rounded-lg backdrop-blur-md"
              >
                Explore Services
              </Button>
            </Link>
          </motion.div>

          <motion.div
            initial="hidden"
            animate={isVisible ? "visible" : "hidden"}
            custom={4}
            variants={fadeIn}
            className="mt-12 flex flex-wrap justify-center gap-x-8 gap-y-4"
          >
            {features.map((feature, index) => (
              <motion.div
                key={feature}
                className="flex items-center px-4 py-2 rounded-full bg-gray-900/40 backdrop-blur-sm border border-gray-700/30"
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{
                  delay: 0.5 + index * 0.1,
                  duration: 0.5,
                }}
                whileHover={{
                  scale: 1.05,
                  backgroundColor: "rgba(124, 58, 237, 0.1)",
                }}
              >
                <CheckCircle className="h-4 w-4 text-purple-400 mr-2" />
                <span className="text-gray-200">{feature}</span>
              </motion.div>
            ))}
          </motion.div>
        </div>
      </div>
    </div>
  );
};

export default HeroSection;
