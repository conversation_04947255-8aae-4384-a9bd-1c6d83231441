"use client";

import React from "react";
import { motion } from "framer-motion";
import { <PERSON>, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { But<PERSON> } from "@/components/ui/button";
import Link from "next/link";
import { <PERSON>R<PERSON>, Brain, Zap, Target, Users, TrendingUp, Eye, MessageSquare } from "lucide-react";

const fadeIn = {
  hidden: { opacity: 0, y: 20 },
  visible: {
    opacity: 1,
    y: 0,
    transition: { duration: 0.6 },
  },
};

const staggerContainer = {
  hidden: { opacity: 0 },
  visible: {
    opacity: 1,
    transition: {
      staggerChildren: 0.2,
    },
  },
};

export default function OfLogicPage() {
  const neuromarketingFeatures = [
    {
      icon: Brain,
      title: "Cognitive Analysis",
      description: "Understand how customers think and make decisions using advanced AI algorithms that analyze behavioral patterns and psychological triggers.",
    },
    {
      icon: Eye,
      title: "Visual Attention Mapping",
      description: "Track and analyze where customers focus their attention, optimizing your content and design for maximum engagement.",
    },
    {
      icon: Target,
      title: "Precision Targeting",
      description: "Reach the right audience with laser-focused campaigns based on psychological profiles and behavioral insights.",
    },
    {
      icon: TrendingUp,
      title: "Conversion Optimization",
      description: "Increase conversion rates by understanding the psychological factors that drive customer decisions and actions.",
    },
  ];

  const processSteps = [
    {
      step: "01",
      title: "Data Collection",
      description: "Gather comprehensive behavioral data from multiple touchpoints including website interactions, social media engagement, and purchase patterns.",
      icon: Brain,
    },
    {
      step: "02",
      title: "AI Analysis",
      description: "Our advanced AI algorithms process the data to identify psychological patterns, preferences, and decision-making triggers.",
      icon: Zap,
    },
    {
      step: "03",
      title: "Insight Generation",
      description: "Transform raw data into actionable insights about customer psychology, motivations, and behavioral tendencies.",
      icon: Target,
    },
    {
      step: "04",
      title: "Strategy Implementation",
      description: "Apply insights to create personalized marketing strategies that resonate with your audience's psychological profile.",
      icon: Users,
    },
  ];

  const testimonials = [
    {
      name: "Sarah Chen",
      role: "Marketing Director",
      company: "TechFlow Solutions",
      quote: "OfLogic helped us understand our customers on a completely new level. Our conversion rates increased by 45% within the first quarter.",
      rating: 5,
    },
    {
      name: "Michael Rodriguez",
      role: "CEO",
      company: "GrowthLab Inc.",
      quote: "The psychological insights from OfLogic transformed our entire marketing approach. We now create campaigns that truly connect with our audience.",
      rating: 5,
    },
    {
      name: "Emily Watson",
      role: "Head of Digital Marketing",
      company: "InnovateCorp",
      quote: "OfLogic's neuromarketing capabilities gave us a competitive edge. We can now predict customer behavior with remarkable accuracy.",
      rating: 5,
    },
  ];

  return (
    <div className="min-h-screen bg-background">
      {/* Hero Section with Brain-Centered Design */}
      <section className="relative py-24 overflow-hidden">
        <div className="absolute inset-0 bg-gradient-to-br from-brand-50 to-brand-100 dark:from-gray-900 dark:to-gray-800" />
        <div className="container mx-auto px-6 relative z-10">
          <motion.div
            initial="hidden"
            animate="visible"
            variants={fadeIn}
            className="text-center max-w-4xl mx-auto"
          >
            <div className="mb-8 flex justify-center">
              <div className="relative">
                <div className="absolute -inset-4 bg-gradient-to-r from-brand-500 to-brand-600 rounded-full opacity-20 blur-lg animate-pulse" />
                <div className="relative p-6 bg-gradient-to-br from-brand-100 to-brand-200 dark:from-brand-800 dark:to-brand-900 rounded-full">
                  <Brain className="h-16 w-16 text-brand-600" />
                </div>
              </div>
            </div>
            <h1 className="text-5xl md:text-6xl font-heading font-bold mb-6 bg-gradient-to-r from-brand-600 to-brand-800 bg-clip-text text-transparent">
              OfLogic
            </h1>
            <p className="text-2xl font-medium mb-4 text-foreground">
              AI-Powered Neuromarketing Intelligence
            </p>
            <p className="text-xl text-muted-foreground mb-8 leading-relaxed">
              Unlock the psychology behind customer behavior with our revolutionary neuromarketing platform. 
              OfLogic combines artificial intelligence with cognitive science to help you understand, predict, 
              and influence customer decisions like never before.
            </p>
            <div className="flex flex-col sm:flex-row gap-4 justify-center">
              <Button size="lg" asChild>
                <Link href="#features">
                  Explore Neuromarketing <ArrowRight className="ml-2 h-4 w-4" />
                </Link>
              </Button>
              <Button variant="outline" size="lg" asChild>
                <Link href="/contact">Request Demo</Link>
              </Button>
            </div>
          </motion.div>
        </div>
      </section>

      {/* What is Neuromarketing Section */}
      <section className="py-20">
        <div className="container mx-auto px-6">
          <div className="grid lg:grid-cols-2 gap-12 items-center">
            <motion.div
              initial="hidden"
              whileInView="visible"
              viewport={{ once: true }}
              variants={fadeIn}
            >
              <h2 className="text-3xl font-heading font-bold mb-6">The Science of Customer Psychology</h2>
              <div className="prose prose-lg text-muted-foreground space-y-4">
                <p>
                  Neuromarketing is the application of neuroscience and cognitive psychology to marketing. 
                  It studies how the brain responds to marketing stimuli, revealing the subconscious factors 
                  that drive purchasing decisions. Traditional marketing relies on what customers say they want, 
                  but neuromarketing reveals what they actually want.
                </p>
                <p>
                  OfLogic harnesses the power of AI to analyze vast amounts of behavioral data, identifying 
                  patterns that would be impossible for humans to detect. Our platform processes everything 
                  from micro-expressions in video content to click patterns on websites, creating a comprehensive 
                  psychological profile of your audience.
                </p>
                <p>
                  By understanding the neurological and psychological triggers that influence decision-making, 
                  you can create marketing campaigns that resonate on a deeper, more emotional level, leading 
                  to higher engagement, stronger brand loyalty, and increased conversions.
                </p>
              </div>
            </motion.div>
            <motion.div
              initial="hidden"
              whileInView="visible"
              viewport={{ once: true }}
              variants={fadeIn}
              className="relative"
            >
              <div className="absolute -inset-4 bg-gradient-to-r from-brand-500 to-brand-600 rounded-lg opacity-20 blur-lg" />
              <Card className="relative">
                <CardHeader>
                  <CardTitle>Neuromarketing Benefits</CardTitle>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div className="flex items-center gap-3">
                    <div className="w-2 h-2 bg-brand-600 rounded-full" />
                    <span>Understand subconscious customer motivations</span>
                  </div>
                  <div className="flex items-center gap-3">
                    <div className="w-2 h-2 bg-brand-600 rounded-full" />
                    <span>Predict purchasing behavior with 85% accuracy</span>
                  </div>
                  <div className="flex items-center gap-3">
                    <div className="w-2 h-2 bg-brand-600 rounded-full" />
                    <span>Optimize content for emotional impact</span>
                  </div>
                  <div className="flex items-center gap-3">
                    <div className="w-2 h-2 bg-brand-600 rounded-full" />
                    <span>Increase conversion rates by up to 45%</span>
                  </div>
                  <div className="flex items-center gap-3">
                    <div className="w-2 h-2 bg-brand-600 rounded-full" />
                    <span>Reduce marketing waste through precision targeting</span>
                  </div>
                  <div className="flex items-center gap-3">
                    <div className="w-2 h-2 bg-brand-600 rounded-full" />
                    <span>Build stronger emotional connections with customers</span>
                  </div>
                </CardContent>
              </Card>
            </motion.div>
          </div>
        </div>
      </section>

      {/* Features Section */}
      <section id="features" className="py-20 bg-muted/50">
        <div className="container mx-auto px-6">
          <motion.div
            initial="hidden"
            whileInView="visible"
            viewport={{ once: true }}
            variants={fadeIn}
            className="text-center max-w-3xl mx-auto mb-16"
          >
            <h2 className="text-3xl font-heading font-bold mb-6">Advanced Neuromarketing Features</h2>
            <p className="text-lg text-muted-foreground">
              Discover how OfLogic's AI-powered neuromarketing tools can transform your understanding of customer behavior.
            </p>
          </motion.div>

          <motion.div
            initial="hidden"
            whileInView="visible"
            viewport={{ once: true }}
            variants={staggerContainer}
            className="grid md:grid-cols-2 gap-8"
          >
            {neuromarketingFeatures.map((feature, index) => (
              <motion.div key={index} variants={fadeIn}>
                <Card className="h-full hover:shadow-lg transition-shadow">
                  <CardHeader>
                    <div className="flex items-center gap-3">
                      <div className="p-2 bg-brand-100 dark:bg-brand-900 rounded-lg">
                        <feature.icon className="h-6 w-6 text-brand-600" />
                      </div>
                      <CardTitle className="text-xl">{feature.title}</CardTitle>
                    </div>
                  </CardHeader>
                  <CardContent>
                    <p className="text-muted-foreground leading-relaxed">{feature.description}</p>
                  </CardContent>
                </Card>
              </motion.div>
            ))}
          </motion.div>
        </div>
      </section>

      {/* Process Section */}
      <section className="py-20">
        <div className="container mx-auto px-6">
          <motion.div
            initial="hidden"
            whileInView="visible"
            viewport={{ once: true }}
            variants={fadeIn}
            className="text-center max-w-3xl mx-auto mb-16"
          >
            <h2 className="text-3xl font-heading font-bold mb-6">How OfLogic Works</h2>
            <p className="text-lg text-muted-foreground">
              Our four-step process transforms raw behavioral data into actionable neuromarketing insights.
            </p>
          </motion.div>

          <motion.div
            initial="hidden"
            whileInView="visible"
            viewport={{ once: true }}
            variants={staggerContainer}
            className="grid md:grid-cols-2 lg:grid-cols-4 gap-8"
          >
            {processSteps.map((step, index) => (
              <motion.div key={index} variants={fadeIn}>
                <Card className="h-full text-center hover:shadow-lg transition-shadow">
                  <CardHeader>
                    <div className="mx-auto mb-4 p-3 bg-brand-100 dark:bg-brand-900 rounded-full w-fit">
                      <step.icon className="h-6 w-6 text-brand-600" />
                    </div>
                    <div className="text-3xl font-bold text-brand-600 mb-2">{step.step}</div>
                    <CardTitle className="text-lg">{step.title}</CardTitle>
                  </CardHeader>
                  <CardContent>
                    <p className="text-muted-foreground text-sm leading-relaxed">{step.description}</p>
                  </CardContent>
                </Card>
              </motion.div>
            ))}
          </motion.div>
        </div>
      </section>

      {/* Testimonials Section */}
      <section className="py-20 bg-muted/50">
        <div className="container mx-auto px-6">
          <motion.div
            initial="hidden"
            whileInView="visible"
            viewport={{ once: true }}
            variants={fadeIn}
            className="text-center max-w-3xl mx-auto mb-16"
          >
            <h2 className="text-3xl font-heading font-bold mb-6">What Our Clients Say</h2>
            <p className="text-lg text-muted-foreground">
              Discover how businesses are transforming their marketing with OfLogic's neuromarketing insights.
            </p>
          </motion.div>

          <motion.div
            initial="hidden"
            whileInView="visible"
            viewport={{ once: true }}
            variants={staggerContainer}
            className="grid lg:grid-cols-3 gap-8"
          >
            {testimonials.map((testimonial, index) => (
              <motion.div key={index} variants={fadeIn}>
                <Card className="h-full hover:shadow-lg transition-shadow">
                  <CardHeader>
                    <div className="flex items-center gap-1 mb-2">
                      {[...Array(testimonial.rating)].map((_, i) => (
                        <div key={i} className="w-4 h-4 bg-yellow-400 rounded-full" />
                      ))}
                    </div>
                    <CardDescription className="text-base italic">
                      "{testimonial.quote}"
                    </CardDescription>
                  </CardHeader>
                  <CardContent>
                    <div className="border-t pt-4">
                      <p className="font-semibold">{testimonial.name}</p>
                      <p className="text-sm text-muted-foreground">{testimonial.role}</p>
                      <p className="text-sm text-brand-600 font-medium">{testimonial.company}</p>
                    </div>
                  </CardContent>
                </Card>
              </motion.div>
            ))}
          </motion.div>
        </div>
      </section>

      {/* CTA Section */}
      <section className="py-20 bg-gradient-to-r from-brand-600 to-brand-700 text-white">
        <div className="container mx-auto px-6">
          <motion.div
            initial="hidden"
            whileInView="visible"
            viewport={{ once: true }}
            variants={fadeIn}
            className="text-center max-w-3xl mx-auto"
          >
            <div className="mb-6 flex justify-center">
              <div className="p-4 bg-white/10 rounded-full">
                <Brain className="h-12 w-12 text-white" />
              </div>
            </div>
            <h2 className="text-3xl font-heading font-bold mb-6">
              Ready to Unlock Customer Psychology?
            </h2>
            <p className="text-xl mb-8 opacity-90">
              Join the neuromarketing revolution and discover what your customers really think.
              Start understanding the psychology behind every purchase decision.
            </p>
            <div className="flex flex-col sm:flex-row gap-4 justify-center">
              <Button size="lg" variant="secondary" asChild>
                <Link href="/contact">
                  Start Your Journey <ArrowRight className="ml-2 h-4 w-4" />
                </Link>
              </Button>
              <Button size="lg" variant="outline" className="border-white text-white hover:bg-white hover:text-brand-600" asChild>
                <Link href="/ofimpact">Explore OfImpact</Link>
              </Button>
            </div>
          </motion.div>
        </div>
      </section>
    </div>
  );
}
