import "./globals.css";
import { Inter, Poppins } from "next/font/google";
import { ThemeProvider } from "@/components/theme-provider";
import Navbar from "@/components/navbar";
import Footer from "@/components/footer";
import { Toaster } from "@/components/ui/sonner";
import type { Metadata } from "next";

const inter = Inter({ 
  subsets: ["latin"],
  variable: "--font-inter",
  display: "swap",
});

const poppins = Poppins({ 
  subsets: ["latin"],
  weight: ["300", "400", "500", "600", "700", "800"],
  variable: "--font-poppins",
  display: "swap",
});

export const metadata: Metadata = {
  title: "Ofstartup.ai | AI-Driven Startup Solutions & Innovation",
  description:
    "Transform your business with Ofstartup.ai's comprehensive AI solutions. We bring the best software and tools with AI to meet all your needs, seamlessly integrated into your company.",
  keywords:
    "AI solutions, startup technology, artificial intelligence, business automation, AI integration, machine learning, digital transformation, startup growth, AI tools, business intelligence",
  openGraph: {
    title: "Ofstartup.ai | AI-Driven Startup Solutions",
    description:
      "Transform your business with comprehensive AI solutions. Seamlessly integrated AI tools and software for modern startups and enterprises.",
    url: "https://www.ofstartup.ai",
    type: "website",
    locale: "en_US",
    siteName: "Ofstartup.ai",
    images: [
      {
        url: "https://www.ofstartup.ai/og-image.jpg",
        width: 1200,
        height: 630,
        alt: "Ofstartup.ai - AI-Driven Solutions",
      },
    ],
  },
  twitter: {
    card: "summary_large_image",
    title: "Ofstartup.ai | AI-Driven Startup Solutions",
    description:
      "Transform your business with comprehensive AI solutions. Seamlessly integrated AI tools and software for modern startups.",
    images: ["https://www.ofstartup.ai/twitter-image.jpg"],
  },
  alternates: {
    canonical: "https://www.ofstartup.ai",
  },
  robots: {
    index: true,
    follow: true,
    googleBot: {
      index: true,
      follow: true,
      "max-video-preview": -1,
      "max-image-preview": "large",
      "max-snippet": -1,
    },
  },
};

interface RootLayoutProps {
  children: React.ReactNode;
}

export default function RootLayout({ children }: RootLayoutProps) {
  return (
    <html lang="en" suppressHydrationWarning className={`${inter.variable} ${poppins.variable}`}>
      <head>
        <meta name="viewport" content="width=device-width, initial-scale=1.0" />
        <meta name="author" content="Ofstartup.ai Team" />
        <link rel="icon" href="/favicon.ico" sizes="any" />
      </head>
      <body className="font-sans antialiased bg-background text-foreground">
        <ThemeProvider
          attribute="class"
          defaultTheme="light"
          enableSystem={true}
          disableTransitionOnChange={false}
        >
          <div className="relative flex min-h-screen flex-col">
            <Navbar />
            <main className="flex-1">
              {children}
            </main>
            <Footer />
          </div>
          <Toaster />
        </ThemeProvider>
      </body>
    </html>
  );
}
