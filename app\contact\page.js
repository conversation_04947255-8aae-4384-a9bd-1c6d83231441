"use client";

import { useState } from "react";
import { motion } from "framer-motion";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import { Mail, MapPin, Phone, Send, CheckCircle2 } from "lucide-react";
import {
  Card,
  CardContent,
  CardHeader,
  CardTitle,
  CardDescription,
} from "@/components/ui/card";
import { Label } from "@/components/ui/label";
import { z } from "zod";
import { toast } from "sonner";

// Define the validation schema using Zod
const contactSchema = z.object({
  name: z.string().min(2, "Full name is required"),
  email: z.string().email("Invalid email address"),
  subject: z.string().min(5, "Subject is required"),
  message: z.string().min(10, "Message is required"),
});

const ContactPage = () => {
  const [isSubmitted, setIsSubmitted] = useState(false);
  const [errors, setErrors] = useState({});

  const [formData, setFormData] = useState({
    name: "",
    email: "",
    subject: "",
    message: "",
  });

  const handleChange = (e) => {
    setFormData({ ...formData, [e.target.id]: e.target.value });
  };

  const handleSubmit = async (e) => {
    e.preventDefault();

    // Validate form data
    const result = contactSchema.safeParse(formData);
    if (!result.success) {
      toast({
        title: "Validation Error",
        description: result.error.errors.map((err) => err.message).join("\n"),
        variant: "destructive",
      });
      return;
    }

    // Send to API
    const response = await fetch("/api/contact", {
      method: "POST",
      headers: { "Content-Type": "application/json" },
      body: JSON.stringify(formData),
    });

    if (response.ok) {
      toast({
        title: "Success!",
        description: "Your message has been sent.",
      });
      setFormData({ name: "", email: "", subject: "", message: "" });
    } else {
      toast({
        title: "Error",
        description: "Failed to send message. Please try again.",
        variant: "destructive",
      });
    }
  };

  const containerVariants = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: { staggerChildren: 0.1, delayChildren: 0.2 },
    },
  };

  const itemVariants = {
    hidden: { y: 20, opacity: 0 },
    visible: {
      y: 0,
      opacity: 1,
      transition: { type: "spring", stiffness: 100, damping: 10 },
    },
  };

  const contactItems = [
    {
      icon: <MapPin className="h-5 w-5 text-primary" />,
      title: "Our Location",
      content: "Bisra Road, Rourkela, Odisha, India - PIN: 769001",
      delay: 0.2,
    },
    {
      icon: <Mail className="h-5 w-5 text-primary" />,
      title: "Email Us",
      content: "<EMAIL>",
      delay: 0.3,
    },
    {
      icon: <Phone className="h-5 w-5 text-primary" />,
      title: "Call Us / WhatsApp",
      content: "+************",
      delay: 0.4,
    },
  ];

  return (
    <section id="contact" className="py-24 relative overflow-hidden">
      <motion.div
        className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 relative z-10"
        initial={{ opacity: 0, y: 20 }}
        whileInView={{ opacity: 1, y: 0 }}
        viewport={{ once: true }}
        transition={{ duration: 0.5 }}
      >
        <motion.div
          className="text-center mb-16 space-y-4"
          initial={{ opacity: 0, y: 20 }}
          whileInView={{ opacity: 1, y: 0 }}
          viewport={{ once: true }}
          transition={{ duration: 0.5, delay: 0.1 }}
        >
          <motion.div
            initial={{ opacity: 0, scale: 0.8 }}
            whileInView={{ opacity: 1, scale: 1 }}
            viewport={{ once: true }}
            transition={{ duration: 0.5 }}
          >
            <span className="inline-block py-1 px-3 text-xs font-medium bg-primary/10 text-primary rounded-full mb-4">
              Get In Touch
            </span>
          </motion.div>
          <motion.h2
            className="text-4xl md:text-5xl font-bold mb-4 bg-clip-text text-transparent bg-gradient-to-r from-primary to-primary/60"
            initial={{ opacity: 0 }}
            whileInView={{ opacity: 1 }}
            viewport={{ once: true }}
            transition={{ duration: 0.5, delay: 0.2 }}
          >
            Ready to Transform Your Business?
          </motion.h2>
          <motion.p
            className="text-xl text-muted-foreground max-w-3xl mx-auto"
            initial={{ opacity: 0 }}
            whileInView={{ opacity: 1 }}
            viewport={{ once: true }}
            transition={{ duration: 0.5, delay: 0.3 }}
          >
            Contact our team today and let&apos;s discuss how we can help you
            achieve your goals.
          </motion.p>
        </motion.div>

        <div className="grid md:grid-cols-2 gap-12">
          {/* Left Column - Contact Information */}
          <motion.div
            variants={containerVariants}
            initial="hidden"
            whileInView="visible"
            viewport={{ once: true }}
            className="space-y-12"
          >
            <Card className="bg-card/50 backdrop-blur-xl border-primary/5 shadow-xl shadow-primary/5 overflow-hidden">
              <CardHeader className="pb-4">
                <CardTitle className="text-2xl font-bold flex items-center gap-2">
                  <motion.div
                    initial={{ scale: 0, opacity: 0 }}
                    whileInView={{ scale: 1, opacity: 1 }}
                    viewport={{ once: true }}
                    transition={{ type: "spring", stiffness: 200, delay: 0.1 }}
                    className="h-8 w-8 rounded-full bg-primary/10 flex items-center justify-center"
                  >
                    <Mail className="h-4 w-4 text-primary" />
                  </motion.div>
                  Contact Information
                </CardTitle>
                <CardDescription>
                  Reach out to us through any of these channels
                </CardDescription>
              </CardHeader>
              <CardContent className="space-y-8">
                <motion.div variants={containerVariants} className="space-y-6">
                  {contactItems.map((item, index) => (
                    <motion.div
                      key={index}
                      variants={itemVariants}
                      className="flex items-start group"
                    >
                      <motion.div
                        className="flex-shrink-0 h-12 w-12 rounded-full bg-primary/10 flex items-center justify-center mr-4 group-hover:bg-primary/20 transition-colors duration-300"
                        whileHover={{ scale: 1.05 }}
                      >
                        {item.icon}
                      </motion.div>
                      <div>
                        <h4 className="font-medium mb-1">{item.title}</h4>
                        <p className="text-muted-foreground">{item.content}</p>
                      </div>
                    </motion.div>
                  ))}
                </motion.div>
              </CardContent>
            </Card>

            <motion.div variants={itemVariants} className="relative">
              <Card className="bg-card/50 backdrop-blur-xl border-primary/5 shadow-xl shadow-primary/5 overflow-hidden">
                <CardHeader>
                  <CardTitle className="text-2xl font-bold">
                    Office Hours
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="grid grid-cols-2 gap-4">
                    <div className="space-y-1">
                      <h4 className="font-medium">Monday - Friday</h4>
                      <p className="text-muted-foreground">
                        9:00 AM - 6:00 PM IST
                      </p>
                    </div>
                    <div className="space-y-1">
                      <h4 className="font-medium">Weekends</h4>
                      <p className="text-muted-foreground">Closed</p>
                    </div>
                  </div>
                </CardContent>
              </Card>
            </motion.div>
          </motion.div>

          {/* Right Column - Contact Form */}
          <motion.div
            initial={{ opacity: 0, x: 20 }}
            whileInView={{ opacity: 1, x: 0 }}
            viewport={{ once: true }}
            transition={{ duration: 0.5, delay: 0.3 }}
          >
            <Card className="bg-card/50 backdrop-blur-xl border-primary/5 shadow-xl shadow-primary/5 h-full relative overflow-hidden">
              {/* Success message overlay */}
              <motion.div
                className="absolute inset-0 bg-card/95 backdrop-blur-sm flex flex-col items-center justify-center z-20 px-8"
                initial={{ opacity: 0, scale: 0.9 }}
                animate={{
                  opacity: isSubmitted ? 1 : 0,
                  scale: isSubmitted ? 1 : 0.9,
                  pointerEvents: isSubmitted ? "auto" : "none",
                }}
                transition={{ type: "spring", stiffness: 100 }}
              >
                <motion.div
                  initial={{ scale: 0 }}
                  animate={{ scale: isSubmitted ? 1 : 0 }}
                  transition={{ delay: 0.2, type: "spring", stiffness: 200 }}
                >
                  <CheckCircle2 className="h-16 w-16 text-primary mb-4" />
                </motion.div>
                <h3 className="text-2xl font-bold mb-2">Message Sent!</h3>
                <p className="text-center text-muted-foreground mb-6">
                  Thank you for reaching out. We&apos;ll get back to you
                  shortly.
                </p>
              </motion.div>

              <CardHeader className="pb-4">
                <CardTitle className="text-2xl font-bold flex items-center gap-2">
                  <motion.div
                    initial={{ scale: 0, opacity: 0 }}
                    whileInView={{ scale: 1, opacity: 1 }}
                    viewport={{ once: true }}
                    transition={{ type: "spring", stiffness: 200, delay: 0.1 }}
                    className="h-8 w-8 rounded-full bg-primary/10 flex items-center justify-center"
                  >
                    <Send className="h-4 w-4 text-primary" />
                  </motion.div>
                  Send Us a Message
                </CardTitle>
                <CardDescription>
                  We typically respond within 24 hours
                </CardDescription>
              </CardHeader>
              <CardContent>
                <form className="space-y-6" onSubmit={handleSubmit}>
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <motion.div variants={itemVariants} className="space-y-2">
                      <Label htmlFor="name">Full Name</Label>
                      <Input
                        id="name"
                        name="name"
                        placeholder="John Doe"
                        className="bg-background/50 border-primary/10 focus:border-primary/30 h-12"
                        required
                      />
                      {errors.name && (
                        <p className="text-red-500 text-sm">{errors.name[0]}</p>
                      )}
                    </motion.div>
                    <motion.div variants={itemVariants} className="space-y-2">
                      <Label htmlFor="email">Email Address</Label>
                      <Input
                        id="email"
                        name="email"
                        type="email"
                        placeholder="<EMAIL>"
                        className="bg-background/50 border-primary/10 focus:border-primary/30 h-12"
                        required
                      />
                      {errors.email && (
                        <p className="text-red-500 text-sm">
                          {errors.email[0]}
                        </p>
                      )}
                    </motion.div>
                  </div>

                  <motion.div variants={itemVariants} className="space-y-2">
                    <Label htmlFor="subject">Subject</Label>
                    <Input
                      id="subject"
                      name="subject"
                      placeholder="How can we help you?"
                      className="bg-background/50 border-primary/10 focus:border-primary/30 h-12"
                      required
                    />
                    {errors.subject && (
                      <p className="text-red-500 text-sm">
                        {errors.subject[0]}
                      </p>
                    )}
                  </motion.div>

                  <motion.div variants={itemVariants} className="space-y-2">
                    <Label htmlFor="message">Message</Label>
                    <Textarea
                      id="message"
                      name="message"
                      placeholder="Tell us about your project..."
                      className="bg-background/50 border-primary/10 focus:border-primary/30 min-h-[180px] resize-none"
                      required
                    />
                    {errors.message && (
                      <p className="text-red-500 text-sm">
                        {errors.message[0]}
                      </p>
                    )}
                  </motion.div>

                  <motion.div variants={itemVariants} className="pt-2">
                    <motion.div
                      whileHover={{ scale: 1.02 }}
                      whileTap={{ scale: 0.98 }}
                    >
                      <Button
                        type="submit"
                        size="lg"
                        className="w-full bg-gradient-to-r from-blue-600 via-purple-600 to-pink-600 hover:from-blue-700 hover:via-purple-700 hover:to-pink-700 text-whited font-medium h-12 shadow-lg"
                      >
                        <Send className="mr-2 h-4 w-4" /> Send Message
                      </Button>
                    </motion.div>
                  </motion.div>
                </form>

                <div className="absolute -bottom-32 -right-32 w-64 h-64 bg-primary/5 rounded-full blur-3xl" />
              </CardContent>
            </Card>
          </motion.div>
        </div>
      </motion.div>
    </section>
  );
};

export default ContactPage;
