"use client";

import { useRef, useEffect, useState } from "react";
import {
  motion,
  useInView,
  useAnimation,
  AnimatePresence,
} from "framer-motion";
import Autoplay from "embla-carousel-autoplay";
import {
  Carousel,
  CarouselContent,
  CarouselItem,
  CarouselNext,
  CarouselPrevious,
} from "@/components/ui/carousel";
import { Card } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { But<PERSON> } from "@/components/ui/button";
import { Star, Quote, ChevronDown, ChevronUp } from "lucide-react";
import Image from "next/image";

const testimonials = [
  {
    name: "<PERSON>",
    position: "CEO, TechStart Inc.",
    content:
      "OfStartup transformed our business with their AI solutions. Their team's expertise and dedication to our success was impressive. They delivered beyond our expectations.",
    rating: 5,
    image:
      "https://images.unsplash.com/photo-1494790108377-be9c29b29330?ixlib=rb-4.0.3&auto=format&fit=crop&w=256&q=80",
  },
  {
    name: "<PERSON>",
    position: "CTO, GrowthBox",
    content:
      "The web development team at OfStartup created an exceptional platform for our e-commerce business. The attention to detail and performance optimization has significantly increased our conversion rates.",
    rating: 5,
    image:
      "https://images.unsplash.com/photo-1560250097-0b93528c311a?ixlib=rb-4.0.3&auto=format&fit=crop&w=256&q=80",
  },
  {
    name: "Emily Rodriguez",
    position: "Founder, InnovateLab",
    content:
      "From business planning to technical execution, OfStartup provided end-to-end support for our startup. Their strategic guidance was invaluable in securing our Series A funding.",
    rating: 5,
    image:
      "https://images.unsplash.com/photo-1573496359142-b8d87734a5a2?ixlib=rb-4.0.3&auto=format&fit=crop&w=256&q=80",
  },
  {
    name: "David Wilson",
    position: "Marketing Director, GlobalTech",
    content:
      "The custom software solution developed by OfStartup has streamlined our operations and reduced costs by 35%. Their team's technical expertise and business acumen make them a valuable partner.",
    rating: 5,
    image:
      "https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?ixlib=rb-4.0.3&auto=format&fit=crop&w=256&q=80",
  },
];

const TestimonialCard = ({ testimonial }) => {
  const [expanded, setExpanded] = useState(false);

  return (
    <Card className="backdrop-blur-lg bg-background/30 border border-white/10 p-6 rounded-xl h-full flex flex-col relative overflow-hidden group hover:border-primary/30 transition-all duration-300 hover:shadow-lg hover:shadow-primary/5">
      <div className="absolute -right-6 -top-6 w-12 h-12 bg-primary/10 rounded-full blur-xl group-hover:bg-primary/20 transition-all duration-300"></div>

      <Quote className="h-8 w-8 text-primary/40 mb-4 opacity-50" />

      <div className="relative flex-grow mb-6">
        <p
          className={`text-gray-300 text-lg leading-relaxed ${
            expanded ? "" : "line-clamp-3"
          }`}
        >
          {testimonial.content}
        </p>

        {testimonial.content.length > 100 && (
          <Button
            variant="ghost"
            size="sm"
            onClick={() => setExpanded(!expanded)}
            className="mt-2 h-8 px-2 text-primary hover:text-primary/80 hover:bg-primary/10"
          >
            {expanded ? (
              <span className="flex items-center text-xs font-medium">
                Show less <ChevronUp className="ml-1 h-3 w-3" />
              </span>
            ) : (
              <span className="flex items-center text-xs font-medium">
                Show more <ChevronDown className="ml-1 h-3 w-3" />
              </span>
            )}
          </Button>
        )}
      </div>

      <div className="flex mb-4">
        {[...Array(testimonial.rating)].map((_, i) => (
          <Star
            key={i}
            className="h-4 w-4 fill-yellow-500 text-yellow-500 mr-1"
          />
        ))}
      </div>

      <div className="flex items-center pt-4 border-t border-white/5">
        <div className="h-12 w-12 rounded-full overflow-hidden mr-4 ring-2 ring-white/10">
          <Image
            fill
            src={testimonial.image}
            alt={testimonial.name}
            className="h-full w-full object-cover"
          />
        </div>
        <div>
          <h4 className="font-semibold text-white">{testimonial.name}</h4>
          <p className="text-sm text-gray-400">{testimonial.position}</p>
        </div>
      </div>
    </Card>
  );
};

const TestimonialsSection = () => {
  const plugin = useRef(Autoplay({ delay: 4000, stopOnInteraction: true }));

  const titleRef = useRef(null);
  const subtitleRef = useRef(null);
  const carouselRef = useRef(null);

  const titleControls = useAnimation();
  const subtitleControls = useAnimation();
  const carouselControls = useAnimation();

  const titleInView = useInView(titleRef, { once: true, threshold: 0.2 });
  const subtitleInView = useInView(subtitleRef, { once: true, threshold: 0.2 });
  const carouselInView = useInView(carouselRef, { once: true, threshold: 0.1 });

  useEffect(() => {
    if (titleInView) {
      titleControls.start({ opacity: 1, y: 0, transition: { duration: 0.6 } });
    }
    if (subtitleInView) {
      subtitleControls.start({
        opacity: 1,
        y: 0,
        transition: { duration: 0.6, delay: 0.2 },
      });
    }
    if (carouselInView) {
      carouselControls.start({
        opacity: 1,
        y: 0,
        transition: { duration: 0.8, delay: 0.3 },
      });
    }
  }, [
    titleInView,
    subtitleInView,
    carouselInView,
    titleControls,
    subtitleControls,
    carouselControls,
  ]);

  return (
    <section
      id="testimonials"
      className="py-16 sm:py-20 md:py-24 relative overflow-hidden"
    >
      {/* Background gradient effects */}
      <div className="absolute inset-0 bg-gradient-to-b from-background via-background/90 to-background/80 z-0"></div>
      <div className="absolute top-0 left-0 w-full h-full bg-grid-white/[0.02]"></div>

      {/* Gradient orbs - responsive positioning */}
      <div className="absolute top-1/4 -left-32 sm:-left-48 md:-left-64 w-48 sm:w-64 md:w-96 h-48 sm:h-64 md:h-96 bg-purple-500/20 rounded-full blur-3xl"></div>
      <div className="absolute bottom-1/3 -right-32 sm:-right-48 md:-right-64 w-48 sm:w-64 md:w-96 h-48 sm:h-64 md:h-96 bg-blue-500/20 rounded-full blur-3xl"></div>

      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 relative z-10">
        <div className="text-center mb-10 sm:mb-16">
          <motion.div
            ref={titleRef}
            initial={{ opacity: 0, y: 20 }}
            animate={titleControls}
          >
            <Badge
              variant="outline"
              className="mb-4 sm:mb-6 py-1 sm:py-1.5 px-3 sm:px-4 text-xs sm:text-sm font-medium bg-background/50 backdrop-blur-sm border-primary/20"
            >
              TESTIMONIALS
            </Badge>
            <h2 className="text-3xl sm:text-4xl md:text-5xl font-bold mb-3 sm:mb-4 bg-clip-text text-transparent bg-gradient-to-r from-white to-white/80">
              Client Success Stories
            </h2>
          </motion.div>

          <motion.p
            ref={subtitleRef}
            initial={{ opacity: 0, y: 20 }}
            animate={subtitleControls}
            className="text-base sm:text-lg md:text-xl text-gray-400 max-w-3xl mx-auto"
          >
            Discover how we&apos;ve helped businesses transform and scale with
            our innovative solutions.
          </motion.p>
        </div>

        <motion.div
          ref={carouselRef}
          initial={{ opacity: 0, y: 30 }}
          animate={carouselControls}
        >
          <div className="relative">
            {/* Left fade effect */}
            <div className="absolute left-0 top-0 h-full w-16 sm:w-24 md:w-32 bg-gradient-to-r from-background to-transparent z-10 pointer-events-none"></div>

            <Carousel
              plugins={[plugin.current]}
              className="w-full"
              opts={{
                align: "start",
                loop: true,
              }}
            >
              <CarouselContent className="-ml-4">
                {testimonials.map((testimonial, index) => (
                  <CarouselItem
                    key={index}
                    className="pl-4 md:basis-1/2 lg:basis-1/3 h-full"
                  >
                    <div className="p-1 h-full">
                      <TestimonialCard testimonial={testimonial} />
                    </div>
                  </CarouselItem>
                ))}
              </CarouselContent>

              <div className="flex justify-center mt-8 sm:mt-10">
                <CarouselPrevious className="mr-2 sm:mr-4 bg-background/30 backdrop-blur-md border-white/10 hover:bg-primary/10 hover:text-primary transition-all" />
                <CarouselNext className="ml-2 sm:ml-4 bg-background/30 backdrop-blur-md border-white/10 hover:bg-primary/10 hover:text-primary transition-all" />
              </div>
            </Carousel>

            {/* Right fade effect */}
            <div className="absolute right-0 top-0 h-full w-16 sm:w-24 md:w-32 bg-gradient-to-l from-background to-transparent z-10 pointer-events-none"></div>
          </div>
        </motion.div>
      </div>
    </section>
  );
};

export default TestimonialsSection;
