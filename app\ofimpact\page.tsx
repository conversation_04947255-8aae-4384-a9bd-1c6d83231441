"use client";

import React from "react";
import { motion } from "framer-motion";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import Link from "next/link";
import { ArrowRight, BarChart3, TrendingUp, DollarSign, Clock, Users, Zap } from "lucide-react";

const fadeIn = {
  hidden: { opacity: 0, y: 20 },
  visible: {
    opacity: 1,
    y: 0,
    transition: { duration: 0.6 },
  },
};

const staggerContainer = {
  hidden: { opacity: 0 },
  visible: {
    opacity: 1,
    transition: {
      staggerChildren: 0.2,
    },
  },
};

export default function OfImpactPage() {
  const benefits = [
    {
      icon: DollarSign,
      title: "25% Cost Reduction",
      description: "Reduce operational costs through intelligent automation and process optimization.",
      metric: "Average savings across all clients",
    },
    {
      icon: TrendingUp,
      title: "40% Productivity Boost",
      description: "Increase team efficiency with AI-powered workflows and smart task management.",
      metric: "Measured productivity improvement",
    },
    {
      icon: Clock,
      title: "24/7 Operations",
      description: "Keep your business running around the clock with automated AI systems.",
      metric: "Continuous availability",
    },
    {
      icon: Users,
      title: "Enhanced Customer Experience",
      description: "Deliver personalized experiences with AI-driven insights and automation.",
      metric: "Improved satisfaction scores",
    },
  ];

  const features = [
    {
      title: "Unified Dashboard",
      description: "Monitor all your business metrics, AI performance, and key insights from a single, intuitive interface.",
      icon: BarChart3,
    },
    {
      title: "Smart Analytics",
      description: "Get actionable insights from your data with advanced AI analytics and predictive modeling.",
      icon: TrendingUp,
    },
    {
      title: "Process Automation",
      description: "Automate repetitive tasks and workflows to free up your team for strategic work.",
      icon: Zap,
    },
    {
      title: "Real-time Monitoring",
      description: "Track performance, identify issues, and optimize operations in real-time.",
      icon: Clock,
    },
  ];

  const useCases = [
    {
      industry: "E-commerce",
      challenge: "Managing inventory and customer service across multiple channels",
      solution: "Automated inventory management, AI chatbots, and predictive analytics",
      result: "30% reduction in stockouts, 50% faster customer response times",
    },
    {
      industry: "Healthcare",
      challenge: "Streamlining patient scheduling and administrative tasks",
      solution: "Intelligent scheduling system and automated documentation",
      result: "40% reduction in administrative overhead, improved patient satisfaction",
    },
    {
      industry: "Manufacturing",
      challenge: "Optimizing production schedules and quality control",
      solution: "AI-powered production planning and automated quality inspection",
      result: "25% increase in production efficiency, 60% reduction in defects",
    },
  ];

  return (
    <div className="min-h-screen bg-background">
      {/* Hero Section */}
      <section className="relative py-24 overflow-hidden">
        <div className="absolute inset-0 bg-gradient-to-br from-brand-50 to-brand-100 dark:from-gray-900 dark:to-gray-800" />
        <div className="container mx-auto px-6 relative z-10">
          <motion.div
            initial="hidden"
            animate="visible"
            variants={fadeIn}
            className="text-center max-w-4xl mx-auto"
          >
            <h1 className="text-5xl md:text-6xl font-heading font-bold mb-6 bg-gradient-to-r from-brand-600 to-brand-800 bg-clip-text text-transparent">
              OfImpact
            </h1>
            <p className="text-2xl font-medium mb-4 text-foreground">
              The All-in-One AI Platform
            </p>
            <p className="text-xl text-muted-foreground mb-8 leading-relaxed">
              Transform your business operations with our comprehensive AI platform that seamlessly integrates 
              all your tools and processes. Experience the power of unified AI-driven business management 
              that reduces costs by 25% while boosting productivity by 40%.
            </p>
            <div className="flex flex-col sm:flex-row gap-4 justify-center">
              <Button size="lg" asChild>
                <Link href="#features">
                  Explore Features <ArrowRight className="ml-2 h-4 w-4" />
                </Link>
              </Button>
              <Button variant="outline" size="lg" asChild>
                <Link href="/contact">Request Demo</Link>
              </Button>
            </div>
          </motion.div>
        </div>
      </section>

      {/* Benefits Section */}
      <section className="py-20">
        <div className="container mx-auto px-6">
          <motion.div
            initial="hidden"
            whileInView="visible"
            viewport={{ once: true }}
            variants={fadeIn}
            className="text-center max-w-3xl mx-auto mb-16"
          >
            <h2 className="text-3xl font-heading font-bold mb-6">Measurable Business Impact</h2>
            <p className="text-lg text-muted-foreground">
              See real results from day one with our proven AI platform that delivers tangible business value.
            </p>
          </motion.div>

          <motion.div
            initial="hidden"
            whileInView="visible"
            viewport={{ once: true }}
            variants={staggerContainer}
            className="grid md:grid-cols-2 lg:grid-cols-4 gap-8"
          >
            {benefits.map((benefit, index) => (
              <motion.div key={index} variants={fadeIn}>
                <Card className="h-full text-center hover:shadow-lg transition-shadow">
                  <CardHeader>
                    <div className="mx-auto mb-4 p-3 bg-brand-100 dark:bg-brand-900 rounded-full w-fit">
                      <benefit.icon className="h-8 w-8 text-brand-600" />
                    </div>
                    <CardTitle className="text-xl">{benefit.title}</CardTitle>
                    <CardDescription className="text-sm text-brand-600 font-medium">
                      {benefit.metric}
                    </CardDescription>
                  </CardHeader>
                  <CardContent>
                    <p className="text-muted-foreground">{benefit.description}</p>
                  </CardContent>
                </Card>
              </motion.div>
            ))}
          </motion.div>
        </div>
      </section>

      {/* Platform Overview */}
      <section className="py-20 bg-muted/50">
        <div className="container mx-auto px-6">
          <div className="grid lg:grid-cols-2 gap-12 items-center">
            <motion.div
              initial="hidden"
              whileInView="visible"
              viewport={{ once: true }}
              variants={fadeIn}
            >
              <h2 className="text-3xl font-heading font-bold mb-6">One Platform, Infinite Possibilities</h2>
              <div className="prose prose-lg text-muted-foreground space-y-4">
                <p>
                  OfImpact brings together all your business tools, data, and processes into a single, 
                  intelligent platform. Our AI-powered system learns from your operations, identifies 
                  optimization opportunities, and automatically implements improvements.
                </p>
                <p>
                  Whether you're managing customer relationships, analyzing sales data, or optimizing 
                  workflows, OfImpact provides the insights and automation you need to stay ahead of 
                  the competition. Our platform scales with your business, adapting to new challenges 
                  and opportunities as they arise.
                </p>
                <p>
                  With OfImpact, you're not just getting software – you're getting a strategic partner 
                  that helps you make smarter decisions, reduce costs, and accelerate growth through 
                  the power of artificial intelligence.
                </p>
              </div>
              <Button size="lg" className="mt-6" asChild>
                <Link href="/contact">
                  Schedule a Demo <ArrowRight className="ml-2 h-4 w-4" />
                </Link>
              </Button>
            </motion.div>
            <motion.div
              initial="hidden"
              whileInView="visible"
              viewport={{ once: true }}
              variants={fadeIn}
              className="relative"
            >
              <div className="absolute -inset-4 bg-gradient-to-r from-brand-500 to-brand-600 rounded-lg opacity-20 blur-lg" />
              <Card className="relative">
                <CardHeader>
                  <CardTitle>Platform Highlights</CardTitle>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div className="flex items-center gap-3">
                    <div className="w-2 h-2 bg-brand-600 rounded-full" />
                    <span>Unified data integration from all business systems</span>
                  </div>
                  <div className="flex items-center gap-3">
                    <div className="w-2 h-2 bg-brand-600 rounded-full" />
                    <span>AI-powered predictive analytics and insights</span>
                  </div>
                  <div className="flex items-center gap-3">
                    <div className="w-2 h-2 bg-brand-600 rounded-full" />
                    <span>Automated workflow optimization</span>
                  </div>
                  <div className="flex items-center gap-3">
                    <div className="w-2 h-2 bg-brand-600 rounded-full" />
                    <span>Real-time performance monitoring</span>
                  </div>
                  <div className="flex items-center gap-3">
                    <div className="w-2 h-2 bg-brand-600 rounded-full" />
                    <span>Scalable cloud infrastructure</span>
                  </div>
                  <div className="flex items-center gap-3">
                    <div className="w-2 h-2 bg-brand-600 rounded-full" />
                    <span>24/7 AI-powered support</span>
                  </div>
                </CardContent>
              </Card>
            </motion.div>
          </div>
        </div>
      </section>
    </div>
  );
}
