"use client";

import React from "react";
import { motion } from "framer-motion";
import { <PERSON>, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import Image from "next/image";
import Link from "next/link";
import { ArrowRight, Users, Target, Lightbulb, Shield } from "lucide-react";

const fadeIn = {
  hidden: { opacity: 0, y: 20 },
  visible: {
    opacity: 1,
    y: 0,
    transition: { duration: 0.6 },
  },
};

const staggerContainer = {
  hidden: { opacity: 0 },
  visible: {
    opacity: 1,
    transition: {
      staggerChildren: 0.2,
    },
  },
};

export default function AboutPage() {
  const teamMembers = [
    {
      name: "<PERSON><PERSON>",
      role: "Co-Founder & CEO",
      bio: "Marshal is a visionary leader with over 12 years of experience in AI and machine learning. He founded Ofstartup.ai with the mission to democratize AI technology for businesses of all sizes. His expertise spans from neural networks to business strategy, making him uniquely positioned to bridge the gap between cutting-edge technology and practical business applications. <PERSON> holds a Master's in Computer Science from IIT Delhi and has previously led AI initiatives at Fortune 500 companies.",
      image: "/team/marshal-tudu.jpg",
      linkedin: "#",
    },
    {
      name: "<PERSON><PERSON><PERSON> <PERSON><PERSON>",
      role: "Co-Founder & CTO",
      bio: "Zunaid is the technical mastermind behind Ofstartup.ai's innovative AI solutions. With a PhD in Artificial Intelligence from Stanford University and 10+ years of experience in developing scalable AI systems, he leads our engineering team in creating breakthrough technologies. His research in deep learning and natural language processing has been published in top-tier conferences. Zunaid's passion for solving complex problems through elegant code drives our technical excellence and innovation.",
      image: "/team/zunaid-ahaal.jpg",
      linkedin: "#",
    },
  ];

  const products = [
    {
      name: "CompletePlace",
      description: "An all-in-one business management platform that integrates CRM, project management, and analytics. CompletePlace uses AI to automate routine tasks, predict customer behavior, and optimize business processes, helping companies reduce operational costs by up to 30%.",
      icon: "🏢",
    },
    {
      name: "Shreya",
      description: "Our intelligent virtual assistant powered by advanced NLP and machine learning. Shreya can handle customer inquiries, schedule appointments, process orders, and provide 24/7 support across multiple channels, improving customer satisfaction while reducing support costs.",
      icon: "🤖",
    },
    {
      name: "Neva",
      description: "A sophisticated data analytics and business intelligence platform that transforms raw data into actionable insights. Neva uses predictive analytics and machine learning to help businesses make data-driven decisions and identify growth opportunities.",
      icon: "📊",
    },
    {
      name: "FlowSuite",
      description: "A comprehensive workflow automation platform that streamlines business processes across departments. FlowSuite uses AI to optimize workflows, reduce manual tasks, and ensure seamless collaboration between teams, boosting productivity by up to 40%.",
      icon: "⚡",
    },
  ];

  const values = [
    {
      title: "Innovation First",
      description: "We embrace cutting-edge AI technologies to solve real-world business challenges.",
      icon: Lightbulb,
    },
    {
      title: "Client Success",
      description: "Your success is our success. We measure our impact by the value we create for you.",
      icon: Target,
    },
    {
      title: "Collaborative Partnership",
      description: "We work as an extension of your team, not just a service provider.",
      icon: Users,
    },
    {
      title: "Ethical AI",
      description: "We develop AI solutions with transparency, fairness, and responsibility at the core.",
      icon: Shield,
    },
  ];

  return (
    <div className="min-h-screen bg-background">
      {/* Hero Section */}
      <section className="relative py-24 overflow-hidden">
        <div className="absolute inset-0 bg-gradient-to-br from-brand-50 to-brand-100 dark:from-gray-900 dark:to-gray-800" />
        <div className="container mx-auto px-6 relative z-10">
          <motion.div
            initial="hidden"
            animate="visible"
            variants={fadeIn}
            className="text-center max-w-4xl mx-auto"
          >
            <h1 className="text-5xl md:text-6xl font-heading font-bold mb-6 bg-gradient-to-r from-brand-600 to-brand-800 bg-clip-text text-transparent">
              About Ofstartup.ai
            </h1>
            <p className="text-xl text-muted-foreground mb-8 leading-relaxed">
              We bring the best software and tools with AI to meet all your needs, seamlessly integrated into your company. 
              Our mission is to transform businesses through intelligent automation and data-driven insights.
            </p>
            <div className="flex flex-col sm:flex-row gap-4 justify-center">
              <Button size="lg" asChild>
                <Link href="#story">
                  Our Story <ArrowRight className="ml-2 h-4 w-4" />
                </Link>
              </Button>
              <Button variant="outline" size="lg" asChild>
                <Link href="#team">Meet the Team</Link>
              </Button>
            </div>
          </motion.div>
        </div>
      </section>

      {/* Our Story Section */}
      <section id="story" className="py-20">
        <div className="container mx-auto px-6">
          <div className="grid lg:grid-cols-2 gap-12 items-center">
            <motion.div
              initial="hidden"
              whileInView="visible"
              viewport={{ once: true }}
              variants={fadeIn}
            >
              <h2 className="text-3xl font-heading font-bold mb-6">Our Story</h2>
              <div className="prose prose-lg text-muted-foreground space-y-4">
                <p>
                  Founded in 2024, Ofstartup.ai emerged from a simple yet powerful vision: to make artificial intelligence 
                  accessible and practical for businesses of all sizes. Our founders, Marshal Tudu and Zunaid Ahaal, 
                  recognized that while AI technology was advancing rapidly, many companies struggled to implement and 
                  benefit from these innovations.
                </p>
                <p>
                  What started as a mission to bridge the AI adoption gap has evolved into a comprehensive platform that 
                  delivers tangible business value. We've helped over 200 companies reduce their operational costs by an 
                  average of 25% while improving efficiency and customer satisfaction.
                </p>
                <p>
                  Today, Ofstartup.ai stands at the forefront of the AI revolution, offering a suite of intelligent 
                  solutions that seamlessly integrate into existing business processes. Our approach combines cutting-edge 
                  technology with deep business understanding, ensuring that every AI implementation drives real, 
                  measurable results.
                </p>
              </div>
            </motion.div>
            <motion.div
              initial="hidden"
              whileInView="visible"
              viewport={{ once: true }}
              variants={fadeIn}
              className="relative"
            >
              <div className="absolute -inset-4 bg-gradient-to-r from-brand-500 to-brand-600 rounded-lg opacity-20 blur-lg" />
              <Card className="relative">
                <CardHeader>
                  <CardTitle>Our Mission</CardTitle>
                  <CardDescription>
                    To democratize AI technology and empower businesses to achieve unprecedented growth through 
                    intelligent automation and data-driven insights.
                  </CardDescription>
                </CardHeader>
                <CardContent>
                  <div className="grid grid-cols-2 gap-4 mt-6">
                    <div className="text-center">
                      <div className="text-3xl font-bold text-brand-600">200+</div>
                      <div className="text-sm text-muted-foreground">Companies Served</div>
                    </div>
                    <div className="text-center">
                      <div className="text-3xl font-bold text-brand-600">25%</div>
                      <div className="text-sm text-muted-foreground">Average Cost Reduction</div>
                    </div>
                    <div className="text-center">
                      <div className="text-3xl font-bold text-brand-600">40%</div>
                      <div className="text-sm text-muted-foreground">Productivity Increase</div>
                    </div>
                    <div className="text-center">
                      <div className="text-3xl font-bold text-brand-600">24/7</div>
                      <div className="text-sm text-muted-foreground">AI Support</div>
                    </div>
                  </div>
                </CardContent>
              </Card>
            </motion.div>
          </div>
        </div>
      </section>

      {/* Our Values Section */}
      <section className="py-20 bg-muted/50">
        <div className="container mx-auto px-6">
          <motion.div
            initial="hidden"
            whileInView="visible"
            viewport={{ once: true }}
            variants={staggerContainer}
            className="text-center max-w-3xl mx-auto mb-16"
          >
            <h2 className="text-3xl font-heading font-bold mb-6">Our Values</h2>
            <p className="text-lg text-muted-foreground">
              These core principles guide everything we do, from developing AI solutions to building lasting partnerships.
            </p>
          </motion.div>

          <motion.div
            initial="hidden"
            whileInView="visible"
            viewport={{ once: true }}
            variants={staggerContainer}
            className="grid md:grid-cols-2 lg:grid-cols-4 gap-8"
          >
            {values.map((value, index) => (
              <motion.div key={index} variants={fadeIn}>
                <Card className="h-full text-center hover:shadow-lg transition-shadow">
                  <CardHeader>
                    <div className="mx-auto mb-4 p-3 bg-brand-100 dark:bg-brand-900 rounded-full w-fit">
                      <value.icon className="h-6 w-6 text-brand-600" />
                    </div>
                    <CardTitle className="text-lg">{value.title}</CardTitle>
                  </CardHeader>
                  <CardContent>
                    <p className="text-muted-foreground">{value.description}</p>
                  </CardContent>
                </Card>
              </motion.div>
            ))}
          </motion.div>
        </div>
      </section>

      {/* Team Section */}
      <section id="team" className="py-20">
        <div className="container mx-auto px-6">
          <motion.div
            initial="hidden"
            whileInView="visible"
            viewport={{ once: true }}
            variants={fadeIn}
            className="text-center max-w-3xl mx-auto mb-16"
          >
            <h2 className="text-3xl font-heading font-bold mb-6">Meet Our Founders</h2>
            <p className="text-lg text-muted-foreground">
              The visionary leaders behind Ofstartup.ai, combining decades of AI expertise with entrepreneurial passion.
            </p>
          </motion.div>

          <motion.div
            initial="hidden"
            whileInView="visible"
            viewport={{ once: true }}
            variants={staggerContainer}
            className="grid md:grid-cols-2 gap-12 max-w-6xl mx-auto"
          >
            {teamMembers.map((member, index) => (
              <motion.div key={index} variants={fadeIn}>
                <Card className="h-full hover:shadow-lg transition-shadow">
                  <CardHeader className="text-center">
                    <div className="relative w-32 h-32 mx-auto mb-4">
                      <div className="absolute inset-0 bg-gradient-to-r from-brand-500 to-brand-600 rounded-full opacity-20" />
                      <div className="relative w-full h-full bg-gradient-to-br from-brand-100 to-brand-200 dark:from-brand-800 dark:to-brand-900 rounded-full flex items-center justify-center">
                        <span className="text-2xl font-bold text-brand-600">
                          {member.name.split(' ').map(n => n[0]).join('')}
                        </span>
                      </div>
                    </div>
                    <CardTitle className="text-xl">{member.name}</CardTitle>
                    <CardDescription className="text-brand-600 font-medium">{member.role}</CardDescription>
                  </CardHeader>
                  <CardContent>
                    <p className="text-muted-foreground leading-relaxed">{member.bio}</p>
                    <Button variant="outline" size="sm" className="mt-4" asChild>
                      <Link href={member.linkedin}>
                        Connect on LinkedIn
                      </Link>
                    </Button>
                  </CardContent>
                </Card>
              </motion.div>
            ))}
          </motion.div>
        </div>
      </section>

      {/* Products Section */}
      <section className="py-20 bg-muted/50">
        <div className="container mx-auto px-6">
          <motion.div
            initial="hidden"
            whileInView="visible"
            viewport={{ once: true }}
            variants={fadeIn}
            className="text-center max-w-3xl mx-auto mb-16"
          >
            <h2 className="text-3xl font-heading font-bold mb-6">Our Products</h2>
            <p className="text-lg text-muted-foreground">
              Discover our suite of AI-powered solutions designed to transform your business operations.
            </p>
          </motion.div>

          <motion.div
            initial="hidden"
            whileInView="visible"
            viewport={{ once: true }}
            variants={staggerContainer}
            className="grid md:grid-cols-2 gap-8"
          >
            {products.map((product, index) => (
              <motion.div key={index} variants={fadeIn}>
                <Card className="h-full hover:shadow-lg transition-shadow">
                  <CardHeader>
                    <div className="flex items-center gap-3">
                      <span className="text-2xl">{product.icon}</span>
                      <CardTitle className="text-xl">{product.name}</CardTitle>
                    </div>
                  </CardHeader>
                  <CardContent>
                    <p className="text-muted-foreground leading-relaxed">{product.description}</p>
                  </CardContent>
                </Card>
              </motion.div>
            ))}
          </motion.div>
        </div>
      </section>

      {/* CTA Section */}
      <section className="py-20 bg-gradient-to-r from-brand-600 to-brand-700 text-white">
        <div className="container mx-auto px-6">
          <motion.div
            initial="hidden"
            whileInView="visible"
            viewport={{ once: true }}
            variants={fadeIn}
            className="text-center max-w-3xl mx-auto"
          >
            <h2 className="text-3xl font-heading font-bold mb-6">
              Ready to Transform Your Business with AI?
            </h2>
            <p className="text-xl mb-8 opacity-90">
              Join hundreds of companies that have already reduced costs by 25% and boosted productivity with our AI solutions.
            </p>
            <div className="flex flex-col sm:flex-row gap-4 justify-center">
              <Button size="lg" variant="secondary" asChild>
                <Link href="/contact">
                  Get Started Today <ArrowRight className="ml-2 h-4 w-4" />
                </Link>
              </Button>
              <Button size="lg" variant="outline" className="border-white text-white hover:bg-white hover:text-brand-600" asChild>
                <Link href="/services">Explore Our Services</Link>
              </Button>
            </div>
          </motion.div>
        </div>
      </section>
    </div>
  );
}
