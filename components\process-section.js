"use client";

import { useState, useRef } from "react";
import { motion, useInView } from "framer-motion";
import { Separator } from "@/components/ui/separator";
import { Card, CardContent } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import {
  CheckCircle2,
  Lightbulb,
  Rocket,
  Search,
  Settings,
  Zap,
} from "lucide-react";

const steps = [
  {
    icon: <Search className="h-6 w-6" />,
    title: "Discovery",
    description:
      "We start by understanding your business, goals, and challenges through in-depth consultations and research.",
  },
  {
    icon: <Lightbulb className="h-6 w-6" />,
    title: "Strategy",
    description:
      "Our team develops a comprehensive strategy tailored to your specific needs and objectives.",
  },
  {
    icon: <Settings className="h-6 w-6" />,
    title: "Development",
    description:
      "We build your solution using cutting-edge technologies and best practices in software development.",
  },
  {
    icon: <CheckCircle2 className="h-6 w-6" />,
    title: "Testing",
    description:
      "Rigorous testing ensures your solution is robust, secure, and delivers an exceptional user experience.",
  },
  {
    icon: <Rocket className="h-6 w-6" />,
    title: "Launch",
    description:
      "We deploy your solution and provide comprehensive training and documentation for your team.",
  },
  {
    icon: <Zap className="h-6 w-6" />,
    title: "Growth",
    description:
      "Ongoing support, maintenance, and optimization to ensure your solution continues to drive results.",
  },
];

const ProcessSection = () => {
  const [hoveredStep, setHoveredStep] = useState(null);
  const sectionRef = useRef(null);
  const isInView = useInView(sectionRef, { once: false, amount: 0.1 });

  return (
    <section
      id="process"
      ref={sectionRef}
      className="py-24 relative overflow-hidden"
    >
      {/* Background gradient and blur effect */}
      <div className="absolute inset-0 bg-gradient-to-b from-background via-background/95 to-background" />
      <motion.div
        className="absolute -top-64 -right-64 w-96 h-96 rounded-full bg-purple-500/20 blur-3xl opacity-20"
        animate={{
          x: [0, 10, 0],
          y: [0, 15, 0],
        }}
        transition={{
          duration: 8,
          repeat: Infinity,
          ease: "easeInOut",
        }}
      />
      <motion.div
        className="absolute -bottom-64 -left-64 w-96 h-96 rounded-full bg-blue-500/20 blur-3xl opacity-20"
        animate={{
          x: [0, -10, 0],
          y: [0, -15, 0],
        }}
        transition={{
          duration: 8,
          repeat: Infinity,
          ease: "easeInOut",
        }}
      />

      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 relative z-10">
        <motion.div
          className="text-center mb-20"
          initial={{ opacity: 0, y: 20 }}
          animate={isInView ? { opacity: 1, y: 0 } : {}}
          transition={{ duration: 0.7 }}
        >
          <Badge
            variant="outline"
            className="mb-4 py-1.5 border-purple-500/50 bg-purple-500/5"
          >
            Our Methodology
          </Badge>
          <h2 className="text-4xl md:text-5xl font-bold mb-6 bg-clip-text text-transparent bg-gradient-to-r from-purple-500 to-blue-500">
            Our Process
          </h2>
          <p className="text-xl text-muted-foreground max-w-3xl mx-auto">
            A systematic approach to delivering exceptional results for your
            business.
          </p>
        </motion.div>

        <div className="relative">
          {/* Timeline line */}
          <motion.div
            className="hidden md:block absolute left-1/2 top-0 bottom-0 w-px"
            initial={{ height: 0 }}
            animate={isInView ? { height: "100%" } : {}}
            transition={{ duration: 1.5, ease: "easeInOut" }}
            style={{
              background:
                "linear-gradient(to bottom, rgb(168, 85, 247), rgb(59, 130, 246))",
            }}
          />

          <div className="space-y-20 md:space-y-24 relative">
            {steps.map((step, index) => {
              const isEven = index % 2 === 0;
              return (
                <motion.div
                  key={index}
                  className={`md:flex items-center ${
                    isEven ? "md:flex-row" : "md:flex-row-reverse"
                  }`}
                  initial={{ opacity: 0, x: isEven ? -30 : 30 }}
                  animate={isInView ? { opacity: 1, x: 0 } : {}}
                  transition={{ duration: 0.7, delay: index * 0.2 }}
                  onMouseEnter={() => setHoveredStep(index)}
                  onMouseLeave={() => setHoveredStep(null)}
                >
                  <div
                    className={`md:w-1/2 ${isEven ? "md:pr-16" : "md:pl-16"}`}
                  >
                    <Card
                      className={`backdrop-blur-sm border border-white/10 shadow-xl transition-all duration-300 ${
                        hoveredStep === index
                          ? "bg-gradient-to-br from-purple-900/30 to-blue-900/30"
                          : "bg-background/60"
                      }`}
                    >
                      <CardContent className="p-6">
                        <div
                          className={`flex items-center ${
                            isEven ? "md:justify-end" : "md:justify-start"
                          }`}
                        >
                          <motion.div
                            className={`flex-shrink-0 flex items-center justify-center h-14 w-14 rounded-full bg-gradient-to-r from-purple-500/80 to-blue-500/80 shadow-lg mb-5 ${
                              isEven ? "md:order-last md:ml-5" : "md:mr-5"
                            }`}
                            whileHover={{ scale: 1.1 }}
                            transition={{
                              type: "spring",
                              stiffness: 400,
                              damping: 10,
                            }}
                          >
                            {step.icon}
                          </motion.div>
                          <h3
                            className={`text-2xl font-bold ${
                              isEven ? "md:text-right" : "md:text-left"
                            }`}
                          >
                            {step.title}
                          </h3>
                        </div>
                        <p
                          className={`text-muted-foreground mt-2 ${
                            isEven ? "md:text-right" : "md:text-left"
                          }`}
                        >
                          {step.description}
                        </p>
                      </CardContent>
                    </Card>
                  </div>

                  {/* Circle on the timeline */}
                  <motion.div
                    className="hidden md:flex absolute left-1/2 transform -translate-x-1/2 items-center justify-center h-12 w-12 rounded-full bg-gradient-to-r from-purple-500 to-blue-500 shadow-lg z-10"
                    initial={{ scale: 0 }}
                    animate={isInView ? { scale: 1 } : {}}
                    transition={{ duration: 0.5, delay: 0.5 + index * 0.2 }}
                  >
                    <span className="text-white font-bold text-lg">
                      {index + 1}
                    </span>
                  </motion.div>

                  <div className="md:w-1/2" />
                </motion.div>
              );
            })}
          </div>
        </div>
      </div>
    </section>
  );
};

export default ProcessSection;
