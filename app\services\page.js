"use client";

import { useRef, useState } from "react";
import { Card, CardContent } from "@/components/ui/card";
import { Ta<PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>ist, TabsTrigger } from "@/components/ui/tabs";
import {
  Brain,
  Code,
  LineChart,
  Lightbulb,
  Rocket,
  Users,
  ChevronRight,
  ArrowRight,
  CheckCircle2,
} from "lucide-react";
import { motion, useScroll, useTransform } from "framer-motion";
import Link from "next/link";
import Navbar from "@/components/navbar";

// Services data from the original component
const services = [
  {
    icon: Lightbulb,
    title: "Business Strategy",
    description:
      "Develop comprehensive business plans, market analysis, and growth strategies tailored to your unique goals and challenges.",
    longDescription:
      "Our business strategy consultants work closely with your team to understand your market position, competitive landscape, and growth objectives. We deliver actionable insights and implementation roadmaps that drive measurable results.",
    benefits: [
      "Comprehensive market analysis and competitor research",
      "Strategic business planning and roadmap development",
      "Growth strategy and expansion planning",
      "Revenue optimization and cost efficiency analysis",
      "Executive coaching and leadership development",
    ],
  },
  {
    icon: Brain,
    title: "AI & ML Solutions",
    description:
      "Leverage cutting-edge artificial intelligence and machine learning technologies to automate processes and gain valuable insights.",
    longDescription:
      "Our AI and machine learning experts design and implement solutions that transform your data into competitive advantages. From predictive analytics to natural language processing, we help you harness the power of AI.",
    benefits: [
      "Custom AI model development and deployment",
      "Machine learning algorithm optimization",
      "Natural language processing solutions",
      "Computer vision and image recognition systems",
      "AI integration with existing business processes",
    ],
  },
  {
    icon: Code,
    title: "Web Development",
    description:
      "Create stunning, responsive websites and web applications that deliver exceptional user experiences and drive conversions.",
    longDescription:
      "Our web development team combines cutting-edge technologies with inspiring design to create digital experiences that engage visitors and convert them into customers. We focus on performance, accessibility, and user-centered design.",
    benefits: [
      "Responsive, mobile-first website design and development",
      "Progressive web application (PWA) development",
      "E-commerce solutions and payment integration",
      "Content management system implementation",
      "Web performance optimization and SEO",
    ],
  },
  {
    icon: Rocket,
    title: "Software Development",
    description:
      "Build custom software solutions that streamline operations, enhance productivity, and solve complex business problems.",
    longDescription:
      "From enterprise applications to mobile solutions, our software development team delivers high-quality, scalable solutions that address your specific business challenges and opportunities.",
    benefits: [
      "Custom enterprise software development",
      "Mobile application development (iOS and Android)",
      "Legacy system modernization",
      "API development and integration",
      "DevOps implementation and CI/CD pipelines",
    ],
  },
  {
    icon: LineChart,
    title: "Data Analytics",
    description:
      "Transform raw data into actionable insights with advanced analytics tools and visualization techniques.",
    longDescription:
      "Our data analytics team helps you harness the power of your data to make informed decisions, identify trends, and discover opportunities for growth and optimization across your organization.",
    benefits: [
      "Business intelligence dashboard development",
      "Data warehouse design and implementation",
      "Big data processing and analytics",
      "Predictive analytics and forecasting",
      "Custom reporting solutions and data visualization",
    ],
  },
  {
    icon: Users,
    title: "Digital Transformation",
    description:
      "Guide your organization through digital transformation with strategic planning and implementation of innovative technologies.",
    longDescription:
      "We partner with businesses to navigate the complexities of digital transformation, ensuring technology investments align with business objectives and deliver meaningful results.",
    benefits: [
      "Digital transformation strategy and roadmap development",
      "Technology stack assessment and modernization",
      "Cloud migration and infrastructure optimization",
      "Business process automation and workflow design",
      "Change management and digital adoption",
    ],
  },
];

// Star component for the background (from original)
const Star = ({ size, left, top, delay }) => {
  return (
    <motion.div
      className="absolute rounded-full bg-white"
      style={{
        width: size,
        height: size,
        left: `${left}%`,
        top: `${top}%`,
      }}
      initial={{ opacity: 0.1, scale: 0.8 }}
      animate={{
        opacity: [0.1, 0.8, 0.1],
        scale: [0.8, 1.2, 0.8],
        boxShadow: [
          "0 0 2px rgba(255, 255, 255, 0.5)",
          "0 0 10px rgba(255, 255, 255, 0.8), 0 0 20px rgba(255, 255, 255, 0.4)",
          "0 0 2px rgba(255, 255, 255, 0.5)",
        ],
      }}
      transition={{
        repeat: Infinity,
        duration: 3 + Math.random() * 3,
        delay: delay,
        ease: "easeInOut",
      }}
    />
  );
};

// Service detail component
const ServiceDetail = ({ service, onRequestQuote }) => {
  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      exit={{ opacity: 0, y: -20 }}
      transition={{ duration: 0.5 }}
      className="space-y-8"
    >
      <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
        <div className="lg:col-span-2 space-y-6">
          <div className="flex items-center gap-4">
            <div className="p-3 rounded-xl bg-gradient-to-br from-purple-500/20 to-purple-700/30 border border-purple-500/20">
              <service.icon className="h-8 w-8 text-purple-400" />
            </div>
            <h2 className="text-2xl md:text-3xl font-bold bg-clip-text text-transparent bg-gradient-to-r from-purple-400 to-indigo-600">
              {service.title}
            </h2>
          </div>

          <p className="text-lg text-gray-300">{service.longDescription}</p>

          <div className="space-y-3">
            <h3 className="text-xl font-semibold text-white">Key Benefits</h3>
            <ul className="space-y-2">
              {service.benefits.map((benefit, idx) => (
                <li key={idx} className="flex items-start gap-3">
                  <CheckCircle2 className="h-5 w-5 text-purple-400 mt-0.5 flex-shrink-0" />
                  <span className="text-gray-300">{benefit}</span>
                </li>
              ))}
            </ul>
          </div>
        </div>

        <div className="lg:col-span-1">
          <Card className="backdrop-blur-lg border border-white/10 bg-black/30 shadow-xl overflow-hidden h-full">
            <CardContent className="p-6 space-y-6">
              <h3 className="text-xl font-semibold text-white">
                Request a Consultation
              </h3>
              <p className="text-gray-400">
                Interested in our {service.title} services? Get in touch for a
                free consultation and discover how we can help your business
                grow.
              </p>
              <motion.button
                onClick={() => onRequestQuote(service.title)}
                className="w-full px-4 py-3 rounded-full bg-gradient-to-r from-blue-600 via-purple-600 to-pink-600 hover:from-blue-700 hover:via-purple-700 hover:to-pink-700 text-white font-medium shadow-lg shadow-purple-500/20 hover:shadow-purple-500/40 transition-all duration-300 flex items-center justify-center gap-2"
                whileHover={{
                  scale: 1.03,
                  boxShadow: "0 10px 25px -5px rgba(168, 85, 247, 0.4)",
                }}
                whileTap={{ scale: 0.98 }}
              >
                <span>Request a Quote</span>
                <ArrowRight className="h-4 w-4" />
              </motion.button>
            </CardContent>
          </Card>
        </div>
      </div>
    </motion.div>
  );
};

// Main Services Page component
const ServicesPage = () => {
  const containerRef = useRef(null);
  const [activeTab, setActiveTab] = useState(
    services[0].title.toLowerCase().replace(/\s+/g, "-")
  );
  const [quoteService, setQuoteService] = useState("");
  const [showSuccessMessage, setShowSuccessMessage] = useState(false);

  const { scrollYProgress } = useScroll({
    target: containerRef,
    offset: ["start end", "end start"],
  });

  // Parallax effects
  const y1 = useTransform(scrollYProgress, [0, 1], [0, -100]);
  const y2 = useTransform(scrollYProgress, [0, 1], [0, -150]);

  // Generate stars data for background
  const starsData = Array.from({ length: 60 }, (_, i) => ({
    id: i,
    size: 1 + Math.random() * 3,
    left: Math.random() * 100,
    top: Math.random() * 100,
    delay: Math.random() * 2,
  }));

  const handleRequestQuote = (serviceName) => {
    setQuoteService(serviceName);
    // In a real implementation, you would show a form modal here
    // For this example, we'll just show a success message
    setShowSuccessMessage(true);
    setTimeout(() => setShowSuccessMessage(false), 3000);
  };

  return (
    <>
      <div className="mt-20">
        {/* Hero Section */}
        <section
          className="pt-24 pb-12 relative overflow-hidden"
          ref={containerRef}
        >
          {/* Background with gradient and stars */}
          <div className="absolute inset-0 bg-gradient-to-b from-background to-background/95 z-0" />
          <div className="absolute inset-0 z-0 opacity-70">
            {starsData.map((star) => (
              <Star
                key={star.id}
                size={star.size}
                left={star.left}
                top={star.top}
                delay={star.delay}
              />
            ))}
          </div>

          {/* Content container */}
          <div className="max-w-7xl mx-auto px-6 lg:px-8 relative z-10">
            {/* Hero content with parallax */}
            <motion.div
              className="text-center max-w-4xl mx-auto"
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6 }}
              style={{ y: y1 }}
            >
              <motion.div
                className="inline-block mb-3"
                initial={{ opacity: 0, scale: 0.8 }}
                animate={{ opacity: 1, scale: 1 }}
                transition={{ duration: 0.5, delay: 0.2 }}
              >
                <div className="px-4 py-1 rounded-full bg-purple-500/10 border border-purple-500/20 text-purple-400 text-sm font-medium">
                  Our Services
                </div>
              </motion.div>

              <motion.h1
                className="text-4xl md:text-6xl font-bold mb-6 bg-clip-text text-transparent bg-gradient-to-r from-purple-400 to-indigo-600"
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.5, delay: 0.3 }}
              >
                Comprehensive Solutions for Modern Business
              </motion.h1>

              <motion.p
                className="text-xl text-gray-400 max-w-3xl mx-auto"
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.5, delay: 0.4 }}
              >
                We deliver innovative strategies and cutting-edge technologies
                to help your business thrive in today&apos;s competitive
                landscape.
              </motion.p>
            </motion.div>
          </div>
        </section>

        {/* Services Overview Grid */}
        <section className="py-16 relative z-10">
          <div className="max-w-7xl mx-auto px-6 lg:px-8">
            <motion.div
              className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8"
              style={{ y: y2 }}
            >
              {services.map((service, index) => {
                const IconComponent = service.icon;
                const serviceId = service.title
                  .toLowerCase()
                  .replace(/\s+/g, "-");

                return (
                  <motion.div
                    key={index}
                    initial={{ opacity: 0, y: 30 }}
                    whileInView={{ opacity: 1, y: 0 }}
                    viewport={{ once: true, margin: "-50px" }}
                    transition={{
                      duration: 0.5,
                      delay: 0.1 + index * 0.1,
                    }}
                  >
                    <motion.div
                      whileHover={{
                        y: -8,
                        transition: { duration: 0.3, ease: "easeOut" },
                      }}
                      className="h-full"
                      onClick={() => {
                        setActiveTab(serviceId);
                        document
                          .getElementById("service-details")
                          .scrollIntoView({ behavior: "smooth" });
                      }}
                    >
                      <Card className="backdrop-blur-lg border border-white/10 bg-black/30 shadow-xl overflow-hidden h-full group relative cursor-pointer">
                        <CardContent className="p-6 flex flex-col h-full">
                          {/* Card glow effect */}
                          <div className="absolute -inset-0.5 bg-gradient-to-r from-purple-500 to-indigo-600 rounded-xl opacity-0 group-hover:opacity-30 blur transition duration-500 group-hover:duration-200" />

                          {/* Icon wrapper */}
                          <motion.div
                            className="p-4 rounded-xl bg-gradient-to-br from-purple-500/10 to-purple-700/20 border border-purple-500/10 mb-6 relative"
                            whileHover={{
                              scale: 1.05,
                              transition: { duration: 0.2 },
                            }}
                          >
                            <div className="flex items-center justify-center bg-clip-text bg-gradient-to-r from-purple-400 to-indigo-500">
                              <IconComponent className="h-10 w-10" />
                            </div>
                          </motion.div>

                          {/* Content */}
                          <h3 className="text-xl font-bold mb-3 text-white group-hover:text-purple-300 transition-colors duration-300">
                            {service.title}
                          </h3>
                          <p className="text-gray-400 group-hover:text-gray-300 transition-colors duration-300">
                            {service.description}
                          </p>

                          <div className="mt-4 pt-4 border-t border-gray-800 flex items-center text-purple-400 group-hover:text-purple-300 transition-colors duration-300">
                            <span className="text-sm font-medium">
                              Learn more
                            </span>
                            <ChevronRight className="h-4 w-4 ml-1 transition-transform group-hover:translate-x-1 duration-300" />
                          </div>
                        </CardContent>
                      </Card>
                    </motion.div>
                  </motion.div>
                );
              })}
            </motion.div>
          </div>
        </section>

        {/* Service Details Section */}
        <section id="service-details" className="py-16 relative z-10">
          <div className="max-w-7xl mx-auto px-6 lg:px-8">
            <motion.div
              initial={{ opacity: 0 }}
              whileInView={{ opacity: 1 }}
              viewport={{ once: true }}
              transition={{ duration: 0.5 }}
            >
              <Tabs
                defaultValue={services[0].title
                  .toLowerCase()
                  .replace(/\s+/g, "-")}
                value={activeTab}
                onValueChange={setActiveTab}
                className="w-full"
              >
                <TabsList className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-6 gap-2 mb-8 bg-black/20 backdrop-blur-md p-1 rounded-lg border border-white/10">
                  {services.map((service) => {
                    const serviceId = service.title
                      .toLowerCase()
                      .replace(/\s+/g, "-");
                    return (
                      <TabsTrigger
                        key={serviceId}
                        value={serviceId}
                        className="data-[state=active]:bg-gradient-to-r data-[state=active]:from-purple-500/30 data-[state=active]:to-indigo-500/30 data-[state=active]:border-purple-500/20 data-[state=active]:text-white data-[state=active]:shadow-md"
                      >
                        {service.title}
                      </TabsTrigger>
                    );
                  })}
                </TabsList>

                {services.map((service) => {
                  const serviceId = service.title
                    .toLowerCase()
                    .replace(/\s+/g, "-");
                  return (
                    <TabsContent key={serviceId} value={serviceId}>
                      <ServiceDetail
                        service={service}
                        onRequestQuote={handleRequestQuote}
                      />
                    </TabsContent>
                  );
                })}
              </Tabs>
            </motion.div>
          </div>
        </section>

        {/* Call to Action */}
        <section className="py-20 relative z-10">
          <div className="max-w-7xl mx-auto px-6 lg:px-8">
            <Card className="backdrop-blur-2xl border border-white/10 bg-gradient-to-r from-purple-900/30 to-indigo-900/30 shadow-2xl overflow-hidden">
              <CardContent className="p-8 md:p-12">
                <div className="grid grid-cols-1 lg:grid-cols-2 gap-8 items-center">
                  <div className="space-y-6">
                    <motion.h2
                      className="text-3xl md:text-4xl font-bold bg-clip-text text-transparent bg-gradient-to-r from-purple-400 to-indigo-300"
                      initial={{ opacity: 0, y: 20 }}
                      whileInView={{ opacity: 1, y: 0 }}
                      viewport={{ once: true }}
                      transition={{ duration: 0.5 }}
                    >
                      Ready to transform your business?
                    </motion.h2>

                    <motion.p
                      className="text-xl text-gray-300"
                      initial={{ opacity: 0, y: 20 }}
                      whileInView={{ opacity: 1, y: 0 }}
                      viewport={{ once: true }}
                      transition={{ duration: 0.5, delay: 0.1 }}
                    >
                      Our team of experts is ready to help you achieve your
                      business goals with customized solutions designed for your
                      unique challenges.
                    </motion.p>

                    <motion.div
                      className="flex flex-wrap gap-4"
                      initial={{ opacity: 0, y: 20 }}
                      whileInView={{ opacity: 1, y: 0 }}
                      viewport={{ once: true }}
                      transition={{ duration: 0.5, delay: 0.2 }}
                    >
                      <Link href="/contact">
                        <motion.button
                          className="px-8 py-3 rounded-full bg-gradient-to-r from-blue-600 via-purple-600 to-pink-600 hover:from-blue-700 hover:via-purple-700 hover:to-pink-700 text-white font-medium shadow-lg shadow-purple-500/20 hover:shadow-purple-500/40 transition-all duration-300 flex items-center gap-2"
                          whileHover={{
                            scale: 1.05,
                            boxShadow:
                              "0 10px 25px -5px rgba(168, 85, 247, 0.4)",
                          }}
                          whileTap={{ scale: 0.98 }}
                        >
                          <span>Schedule a Consultation</span>
                          <ArrowRight className="h-4 w-4" />
                        </motion.button>
                      </Link>
                    </motion.div>
                  </div>

                  <div className="lg:pl-8 lg:border-l border-white/10">
                    <motion.div
                      className="space-y-4"
                      initial={{ opacity: 0, x: 20 }}
                      whileInView={{ opacity: 1, x: 0 }}
                      viewport={{ once: true }}
                      transition={{ duration: 0.5 }}
                    >
                      <div className="flex items-center gap-3">
                        <CheckCircle2 className="h-5 w-5 text-green-400" />
                        <span className="text-gray-200">
                          Expert team with industry-specific knowledge
                        </span>
                      </div>
                      <div className="flex items-center gap-3">
                        <CheckCircle2 className="h-5 w-5 text-green-400" />
                        <span className="text-gray-200">
                          Customized solutions tailored to your needs
                        </span>
                      </div>
                      <div className="flex items-center gap-3">
                        <CheckCircle2 className="h-5 w-5 text-green-400" />
                        <span className="text-gray-200">
                          Transparent pricing and project management
                        </span>
                      </div>
                      <div className="flex items-center gap-3">
                        <CheckCircle2 className="h-5 w-5 text-green-400" />
                        <span className="text-gray-200">
                          Ongoing support and optimization
                        </span>
                      </div>
                      <div className="flex items-center gap-3">
                        <CheckCircle2 className="h-5 w-5 text-green-400" />
                        <span className="text-gray-200">
                          Proven results and satisfied clients
                        </span>
                      </div>
                    </motion.div>
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>
        </section>
      </div>
    </>
  );
};

export default ServicesPage;
