@import "tailwindcss";
@import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&family=Poppins:wght@300;400;500;600;700;800&display=swap');

@plugin "tailwindcss-animate";

@custom-variant dark (&:is(.dark *));

@theme {
  --font-sans: 'Inter', var(--font-geist-sans);
  --font-heading: 'Poppins', var(--font-geist-sans);
  --font-mono: var(--font-geist-mono);
}

:root {
  /* Professional SaaS Light Theme */
  --background: 248 250 252; /* slate-50 */
  --foreground: 15 23 42; /* slate-900 */
  --card: 255 255 255; /* white */
  --card-foreground: 15 23 42; /* slate-900 */
  --popover: 255 255 255; /* white */
  --popover-foreground: 15 23 42; /* slate-900 */
  --primary: 14 165 233; /* sky-500 */
  --primary-foreground: 248 250 252; /* slate-50 */
  --secondary: 241 245 249; /* slate-100 */
  --secondary-foreground: 30 41 59; /* slate-800 */
  --muted: 241 245 249; /* slate-100 */
  --muted-foreground: 100 116 139; /* slate-500 */
  --accent: 226 232 240; /* slate-200 */
  --accent-foreground: 30 41 59; /* slate-800 */
  --destructive: 239 68 68; /* red-500 */
  --destructive-foreground: 248 250 252; /* slate-50 */
  --border: 226 232 240; /* slate-200 */
  --input: 226 232 240; /* slate-200 */
  --ring: 14 165 233; /* sky-500 */
  --chart-1: oklch(0.646 0.222 41.116);
  --chart-2: oklch(0.6 0.118 184.704);
  --chart-3: oklch(0.398 0.07 227.392);
  --chart-4: oklch(0.828 0.189 84.429);
  --chart-5: oklch(0.769 0.188 70.08);
  --radius: 0.625rem;
  --sidebar: oklch(0.985 0 0);
  --sidebar-foreground: oklch(0.145 0 0);
  --sidebar-primary: oklch(0.205 0 0);
  --sidebar-primary-foreground: oklch(0.985 0 0);
  --sidebar-accent: oklch(0.97 0 0);
  --sidebar-accent-foreground: oklch(0.205 0 0);
  --sidebar-border: oklch(0.922 0 0);
  --sidebar-ring: oklch(0.708 0 0);
}

.dark {
  /* Professional SaaS Dark Theme */
  --background: 2 6 23; /* slate-950 */
  --foreground: 248 250 252; /* slate-50 */
  --card: 15 23 42; /* slate-900 */
  --card-foreground: 248 250 252; /* slate-50 */
  --popover: 15 23 42; /* slate-900 */
  --popover-foreground: 248 250 252; /* slate-50 */
  --primary: 56 189 248; /* sky-400 */
  --primary-foreground: 2 6 23; /* slate-950 */
  --secondary: 30 41 59; /* slate-800 */
  --secondary-foreground: 248 250 252; /* slate-50 */
  --muted: 30 41 59; /* slate-800 */
  --muted-foreground: 148 163 184; /* slate-400 */
  --accent: 51 65 85; /* slate-700 */
  --accent-foreground: 248 250 252; /* slate-50 */
  --destructive: 248 113 113; /* red-400 */
  --destructive-foreground: 2 6 23; /* slate-950 */
  --border: 30 41 59; /* slate-800 */
  --input: 30 41 59; /* slate-800 */
  --ring: 56 189 248; /* sky-400 */
  --chart-1: oklch(0.488 0.243 264.376);
  --chart-2: oklch(0.696 0.17 162.48);
  --chart-3: oklch(0.769 0.188 70.08);
  --chart-4: oklch(0.627 0.265 303.9);
  --chart-5: oklch(0.645 0.246 16.439);
  --sidebar: oklch(0.205 0 0);
  --sidebar-foreground: oklch(0.985 0 0);
  --sidebar-primary: oklch(0.488 0.243 264.376);
  --sidebar-primary-foreground: oklch(0.985 0 0);
  --sidebar-accent: oklch(0.269 0 0);
  --sidebar-accent-foreground: oklch(0.985 0 0);
  --sidebar-border: oklch(0.269 0 0);
  --sidebar-ring: oklch(0.439 0 0);
}

@theme inline {
  --color-background: var(--background);
  --color-foreground: var(--foreground);
  --color-card: var(--card);
  --color-card-foreground: var(--card-foreground);
  --color-popover: var(--popover);
  --color-popover-foreground: var(--popover-foreground);
  --color-primary: var(--primary);
  --color-primary-foreground: var(--primary-foreground);
  --color-secondary: var(--secondary);
  --color-secondary-foreground: var(--secondary-foreground);
  --color-muted: var(--muted);
  --color-muted-foreground: var(--muted-foreground);
  --color-accent: var(--accent);
  --color-accent-foreground: var(--accent-foreground);
  --color-destructive: var(--destructive);
  --color-destructive-foreground: var(--destructive-foreground);
  --color-border: var(--border);
  --color-input: var(--input);
  --color-ring: var(--ring);
  --color-chart-1: var(--chart-1);
  --color-chart-2: var(--chart-2);
  --color-chart-3: var(--chart-3);
  --color-chart-4: var(--chart-4);
  --color-chart-5: var(--chart-5);
  --radius-sm: calc(var(--radius) - 4px);
  --radius-md: calc(var(--radius) - 2px);
  --radius-lg: var(--radius);
  --radius-xl: calc(var(--radius) + 4px);
  --color-sidebar: var(--sidebar);
  --color-sidebar-foreground: var(--sidebar-foreground);
  --color-sidebar-primary: var(--sidebar-primary);
  --color-sidebar-primary-foreground: var(--sidebar-primary-foreground);
  --color-sidebar-accent: var(--sidebar-accent);
  --color-sidebar-accent-foreground: var(--sidebar-accent-foreground);
  --color-sidebar-border: var(--sidebar-border);
  --color-sidebar-ring: var(--sidebar-ring);
}

@layer base {
  * {
    @apply border-border outline-ring/50;
  }
  body {
    @apply bg-background text-foreground font-sans antialiased;
  }
  h1, h2, h3, h4, h5, h6 {
    @apply font-heading;
  }
}

/* Custom animations for professional SaaS feel */
@layer utilities {
  .animate-fade-in {
    animation: fade-in 0.5s ease-out;
  }

  .animate-slide-up {
    animation: slide-up 0.6s ease-out;
  }

  .animate-scale-in {
    animation: scale-in 0.4s ease-out;
  }
}

@keyframes fade-in {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes slide-up {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes scale-in {
  from {
    opacity: 0;
    transform: scale(0.95);
  }
  to {
    opacity: 1;
    transform: scale(1);
  }
}
