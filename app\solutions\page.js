"use client";

import { useState, useRef, useEffect } from "react";
import {
  motion,
  AnimatePresence,
  useInView,
  useScroll,
  useTransform,
} from "framer-motion";
import { Tabs, Ta<PERSON>Content, Ta<PERSON>List, TabsTrigger } from "@/components/ui/tabs";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardFooter } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Separator } from "@/components/ui/separator";
import { ScrollArea, ScrollBar } from "@/components/ui/scroll-area";
import { Progress } from "@/components/ui/progress";
import {
  CheckCircle2,
  ChevronRight,
  Code,
  Building,
  BrainCircuit,
  Database,
  Globe,
  Shield,
  BarChart4,
  Smartphone,
  Zap,
  Clock,
  LineChart,
  Users,
  CheckCircle,
  Award,
  ArrowRight,
  ExternalLink,
  CloudCog,
  ServerCog,
  GitBranch,
  CircuitBoard,
  MessageSquareText,
  Eye,
  FileSearch,
  Network,
  RefreshCcw,
  Server,
  Coins,
  DraftingCompass,
  FileCheck,
  AlertTriangle,
  Lock,
  Link2,
} from "lucide-react";
import Link from "next/link";

// Solution categories with expanded details
const solutionCategories = [
  {
    id: "tech",
    title: "Tech Solutions",
    description:
      "Cutting-edge technology solutions built with the latest tech stacks to transform your digital presence and capabilities.",
    icon: Code,
    color: "blue",
    gradientFrom: "from-blue-600",
    gradientTo: "to-cyan-600",
    features: [
      "Full-Stack Development",
      "Modern Frontend Frameworks",
      "Scalable Backend Architecture",
      "Cloud-Native Solutions",
      "DevOps & CI/CD Implementation",
      "Mobile & Cross-Platform Apps",
    ],
    stat: { value: "99.9%", label: "Uptime for deployed solutions" },
  },
  {
    id: "ai",
    title: "AI & ML Solutions",
    description:
      "Harness the power of artificial intelligence and machine learning to gain insights, automate processes, and create intelligent products.",
    icon: BrainCircuit,
    color: "indigo",
    gradientFrom: "from-indigo-600",
    gradientTo: "to-violet-600",
    features: [
      "Custom AI Model Development",
      "NLP & Computer Vision Solutions",
      "Predictive Analytics",
      "ML Ops & Model Deployment",
      "Data Pipeline Automation",
      "AI Product Strategy",
    ],
    stat: { value: "3.5x", label: "Avg. efficiency improvement" },
  },
  {
    id: "business",
    title: "Business Solutions",
    description:
      "Strategic business solutions that help you plan, launch, and scale your vision in competitive markets.",
    icon: Building,
    color: "emerald",
    gradientFrom: "from-emerald-600",
    gradientTo: "to-teal-600",
    features: [
      "MVP Development & Validation",
      "Go-to-Market Strategy",
      "Digital Transformation",
      "Process Optimization",
      "Growth & Scaling Frameworks",
      "Business Intelligence",
    ],
    stat: { value: "87%", label: "Success rate for launched products" },
  },
  {
    id: "data",
    title: "Data Solutions",
    description:
      "Transform raw data into actionable intelligence with our comprehensive data solutions and analytics platforms.",
    icon: Database,
    color: "amber",
    gradientFrom: "from-amber-600",
    gradientTo: "to-orange-600",
    features: [
      "Big Data Engineering",
      "Data Warehousing",
      "Business Intelligence Dashboards",
      "ETL Pipeline Development",
      "Real-time Analytics",
      "Data Strategy Consulting",
    ],
    stat: { value: "2.8x", label: "Avg. ROI on data projects" },
  },
  {
    id: "web3",
    title: "Web3 & Blockchain",
    description:
      "Step into the future with decentralized solutions built on cutting-edge blockchain technology and Web3 infrastructure.",
    icon: Globe,
    color: "purple",
    gradientFrom: "from-purple-600",
    gradientTo: "to-pink-600",
    features: [
      "Smart Contract Development",
      "Decentralized Applications (dApps)",
      "Tokenization & NFT Platforms",
      "Blockchain Integration",
      "Web3 Frontend Development",
      "DAO Infrastructure",
    ],
    stat: { value: "78%", label: "Reduction in transaction costs" },
  },
  {
    id: "security",
    title: "Security Solutions",
    description:
      "Protect your digital assets with enterprise-grade security solutions designed to address modern threats.",
    icon: Shield,
    color: "red",
    gradientFrom: "from-red-600",
    gradientTo: "to-rose-600",
    features: [
      "Application Security Testing",
      "Penetration Testing",
      "Security Architecture Design",
      "Compliance & Audit Support",
      "DevSecOps Implementation",
      "Incident Response Planning",
    ],
    stat: { value: "94%", label: "Threat detection accuracy" },
  },
];

// Expanded solutions data
const expandedSolutions = {
  tech: [
    {
      id: "fullstack",
      title: "Full-Stack Development",
      icon: Code,
      description:
        "End-to-end development services that seamlessly integrate front-end user experiences with robust back-end functionality.",
      capabilities: [
        "Custom web application development",
        "API development and integration",
        "Database design and optimization",
        "Serverless architecture implementation",
        "Performance optimization",
        "Code review and refactoring",
      ],
      technologies: [
        { name: "React/Next.js", level: 95 },
        { name: "Node.js", level: 90 },
        { name: "TypeScript", level: 92 },
        { name: "Python/Django", level: 88 },
        { name: "GraphQL", level: 85 },
      ],
    },
    {
      id: "frontend",
      title: "Modern Frontend Frameworks",
      icon: Zap,
      description:
        "Building exceptional user interfaces with the latest frontend technologies for optimal performance and user experience.",
      capabilities: [
        "Component-based architecture",
        "Responsive design implementation",
        "State management solutions",
        "Animation and micro-interactions",
        "Accessibility compliance",
        "Cross-browser compatibility",
      ],
      technologies: [
        { name: "React/Next.js", level: 96 },
        { name: "Vue.js/Nuxt", level: 88 },
        { name: "Angular", level: 85 },
        { name: "TailwindCSS", level: 95 },
        { name: "Framer Motion", level: 92 },
      ],
    },
    {
      id: "backend",
      title: "Scalable Backend Architecture",
      icon: ServerCog,
      description:
        "Building robust, scalable backend systems that can handle growing demands and complex business requirements.",
      capabilities: [
        "Microservices architecture",
        "RESTful API development",
        "Authentication & authorization systems",
        "Caching strategies",
        "Database scaling solutions",
        "Asynchronous processing",
      ],
      technologies: [
        { name: "Node.js", level: 92 },
        { name: "Python", level: 90 },
        { name: "Java Spring", level: 85 },
        { name: ".NET Core", level: 84 },
        { name: "Go", level: 82 },
      ],
    },
    {
      id: "cloud",
      title: "Cloud-Native Solutions",
      icon: CloudCog,
      description:
        "Leveraging the full potential of cloud platforms to build scalable, resilient, and cost-effective applications.",
      capabilities: [
        "Multi-cloud strategy",
        "Infrastructure as Code (IaC)",
        "Containerization with Docker",
        "Kubernetes orchestration",
        "Serverless architecture",
        "Auto-scaling implementations",
      ],
      technologies: [
        { name: "AWS", level: 94 },
        { name: "Google Cloud", level: 90 },
        { name: "Azure", level: 88 },
        { name: "Terraform", level: 89 },
        { name: "Kubernetes", level: 91 },
      ],
    },
    {
      id: "devops",
      title: "DevOps & CI/CD Implementation",
      icon: GitBranch,
      description:
        "Streamlining development workflows with automated pipelines for continuous integration and deployment.",
      capabilities: [
        "CI/CD pipeline setup",
        "Automated testing integration",
        "Infrastructure automation",
        "Monitoring and logging",
        "Release management",
        "Environment management",
      ],
      technologies: [
        { name: "GitHub Actions", level: 93 },
        { name: "Jenkins", level: 90 },
        { name: "CircleCI", level: 88 },
        { name: "Docker", level: 94 },
        { name: "Ansible", level: 85 },
      ],
    },
    {
      id: "mobile",
      title: "Mobile & Cross-Platform Apps",
      icon: Smartphone,
      description:
        "Developing high-performance mobile applications that work seamlessly across multiple platforms and devices.",
      capabilities: [
        "Native iOS & Android development",
        "Cross-platform development",
        "Progressive Web Apps (PWAs)",
        "Offline functionality",
        "Push notifications",
        "App store optimization",
      ],
      technologies: [
        { name: "React Native", level: 92 },
        { name: "Flutter", level: 89 },
        { name: "Swift", level: 87 },
        { name: "Kotlin", level: 86 },
        { name: "Ionic", level: 84 },
      ],
    },
  ],
  ai: [
    {
      id: "aimodels",
      title: "Custom AI Model Development",
      icon: BrainCircuit,
      description:
        "Building specialized AI models tailored to your specific business challenges and data requirements.",
      capabilities: [
        "Deep learning model architecture",
        "Transfer learning implementation",
        "Model fine-tuning & optimization",
        "Custom loss functions",
        "Ensemble methods",
        "Hyperparameter optimization",
      ],
      technologies: [
        { name: "TensorFlow", level: 93 },
        { name: "PyTorch", level: 92 },
        { name: "Keras", level: 90 },
        { name: "Scikit-learn", level: 95 },
        { name: "ONNX", level: 85 },
      ],
    },
    {
      id: "nlp",
      title: "NLP & Computer Vision Solutions",
      icon: Eye,
      description:
        "Leveraging natural language processing and computer vision to create intelligent applications that understand text and images.",
      capabilities: [
        "Text classification & sentiment analysis",
        "Named entity recognition",
        "Question answering systems",
        "Image classification & object detection",
        "Optical character recognition",
        "Video analytics",
      ],
      technologies: [
        { name: "Hugging Face", level: 92 },
        { name: "spaCy", level: 90 },
        { name: "OpenCV", level: 94 },
        { name: "YOLO", level: 88 },
        { name: "BERT/GPT", level: 91 },
      ],
    },
    {
      id: "predictive",
      title: "Predictive Analytics",
      icon: LineChart,
      description:
        "Using historical data to build predictive models that forecast future trends and inform strategic decisions.",
      capabilities: [
        "Time series forecasting",
        "Churn prediction",
        "Demand forecasting",
        "Anomaly detection",
        "Recommendation systems",
        "Risk modeling",
      ],
      technologies: [
        { name: "Prophet", level: 90 },
        { name: "XGBoost", level: 94 },
        { name: "LightGBM", level: 92 },
        { name: "ARIMA", level: 86 },
        { name: "CatBoost", level: 88 },
      ],
    },
    {
      id: "mlops",
      title: "ML Ops & Model Deployment",
      icon: RefreshCcw,
      description:
        "Streamlining the entire machine learning lifecycle from development to production with robust MLOps practices.",
      capabilities: [
        "Model versioning & tracking",
        "Feature store implementation",
        "Automated retraining",
        "Model A/B testing",
        "Production monitoring",
        "Model explainability",
      ],
      technologies: [
        { name: "MLflow", level: 92 },
        { name: "Kubeflow", level: 88 },
        { name: "TFServing", level: 89 },
        { name: "BentoML", level: 84 },
        { name: "DVC", level: 86 },
      ],
    },
    {
      id: "datapipeline",
      title: "Data Pipeline Automation",
      icon: Network,
      description:
        "Building efficient data pipelines that automate the collection, processing, and delivery of data for AI/ML workloads.",
      capabilities: [
        "ETL/ELT pipeline development",
        "Stream processing",
        "Data quality validation",
        "Feature engineering automation",
        "Distributed processing",
        "Data lineage tracking",
      ],
      technologies: [
        { name: "Airflow", level: 93 },
        { name: "Spark", level: 90 },
        { name: "Kafka", level: 89 },
        { name: "dbt", level: 87 },
        { name: "Luigi", level: 85 },
      ],
    },
    {
      id: "aistrategy",
      title: "AI Product Strategy",
      icon: BarChart4,
      description:
        "Helping businesses identify opportunities for AI implementation and develop a strategic roadmap for intelligent products.",
      capabilities: [
        "AI opportunity assessment",
        "POC development",
        "Data readiness evaluation",
        "Implementation roadmapping",
        "AI governance framework",
        "ROI calculation",
      ],
      technologies: [
        { name: "Strategic Planning", level: 95 },
        { name: "Risk Assessment", level: 92 },
        { name: "Agile Methodology", level: 94 },
        { name: "Cost-Benefit Analysis", level: 93 },
        { name: "Change Management", level: 90 },
      ],
    },
  ],
  business: [
    {
      id: "mvp",
      title: "MVP Development & Validation",
      icon: Zap,
      description:
        "Rapidly build and validate minimum viable products to test business ideas with real users while minimizing costs.",
      capabilities: [
        "Lean MVP roadmapping",
        "Feature prioritization",
        "Rapid prototyping",
        "User testing coordination",
        "Feedback collection systems",
        "Iterative development cycles",
      ],
      technologies: [
        { name: "Design Thinking", level: 95 },
        { name: "Lean Startup", level: 94 },
        { name: "Agile Development", level: 93 },

        { name: "User Research", level: 90 },
        { name: "A/B Testing", level: 92 },
      ],
    },
    {
      id: "gotomarket",
      title: "Go-to-Market Strategy",
      icon: LineChart,
      description:
        "Comprehensive strategies to successfully launch and scale products in competitive markets with maximum impact.",
      capabilities: [
        "Market analysis & segmentation",
        "Competitive positioning",
        "Pricing strategy development",
        "Channel strategy",
        "Marketing & sales alignment",
        "Launch planning & execution",
      ],
      technologies: [
        { name: "Market Research", level: 94 },
        { name: "Customer Journey Mapping", level: 92 },
        { name: "Growth Hacking", level: 90 },
        { name: "CRM Implementation", level: 88 },
        { name: "Performance Marketing", level: 93 },
      ],
    },
    {
      id: "digital",
      title: "Digital Transformation",
      icon: RefreshCcw,
      description:
        "Guiding organizations through comprehensive digital transformation journeys to become more agile and competitive.",
      capabilities: [
        "Digital maturity assessment",
        "Technology roadmapping",
        "Process digitization",
        "Legacy system modernization",
        "Culture & change management",
        "Digital capability building",
      ],
      technologies: [
        { name: "Enterprise Architecture", level: 92 },
        { name: "Change Management", level: 95 },
        { name: "Process Mining", level: 88 },
        { name: "Digital Workplace Tools", level: 90 },
        { name: "Agile Transformation", level: 93 },
      ],
    },
    {
      id: "process",
      title: "Process Optimization",
      icon: Clock,
      description:
        "Streamlining business processes to eliminate inefficiencies, reduce costs, and improve quality and customer satisfaction.",
      capabilities: [
        "Business process mapping",
        "Bottleneck identification",
        "Workflow automation",
        "Resource optimization",
        "KPI development & tracking",
        "Continuous improvement programs",
      ],
      technologies: [
        { name: "Lean Six Sigma", level: 94 },
        { name: "BPM Software", level: 90 },
        { name: "Process Mining", level: 89 },
        { name: "RPA", level: 92 },
        { name: "Value Stream Mapping", level: 93 },
      ],
    },
    {
      id: "growth",
      title: "Growth & Scaling Frameworks",
      icon: BarChart4,
      description:
        "Systematic approaches to accelerate growth and scale operations efficiently while maintaining quality and culture.",
      capabilities: [
        "Growth model development",
        "Scalable operations design",
        "Expansion strategy",
        "Team scaling frameworks",
        "Growth metrics & analytics",
        "Funding strategy",
      ],
      technologies: [
        { name: "OKR Framework", level: 95 },
        { name: "Growth Marketing", level: 92 },
        { name: "Scaling Playbooks", level: 94 },
        { name: "Metrics Dashboards", level: 93 },
        { name: "Venture Capital Advisory", level: 88 },
      ],
    },
    {
      id: "intelligence",
      title: "Business Intelligence",
      icon: FileSearch,
      description:
        "Converting raw data into actionable insights through advanced analytics, visualization, and reporting solutions.",
      capabilities: [
        "Data warehouse implementation",
        "Dashboard development",
        "Advanced analytics",
        "Self-service BI enablement",
        "Predictive analytics integration",
        "Data governance setup",
      ],
      technologies: [
        { name: "Tableau", level: 94 },
        { name: "Power BI", level: 93 },
        { name: "Looker", level: 91 },
        { name: "SQL", level: 95 },
        { name: "Python Analytics", level: 90 },
      ],
    },
  ],
  data: [
    {
      id: "bigdata",
      title: "Big Data Engineering",
      icon: Database,
      description:
        "Harnessing the power of big data with scalable infrastructure and processing solutions to unlock actionable insights.",
      capabilities: [
        "Distributed data processing",
        "Data lake implementation",
        "High-volume data ingestion",
        "Data partitioning & optimization",
        "Real-time data streaming",
        "Big data architecture design",
      ],
      technologies: [
        { name: "Apache Hadoop", level: 92 },
        { name: "Apache Spark", level: 94 },
        { name: "Kafka", level: 90 },
        { name: "Flink", level: 88 },
        { name: "Snowflake", level: 91 },
      ],
    },
    {
      id: "datawarehousing",
      title: "Data Warehousing",
      icon: Server,
      description:
        "Building robust data warehousing solutions to centralize and manage structured data for advanced analytics.",
      capabilities: [
        "Schema design & modeling",
        "Data integration & consolidation",
        "Performance tuning",
        "Data archiving strategies",
        "Cloud warehouse deployment",
        "Multi-source data unification",
      ],
      technologies: [
        { name: "Snowflake", level: 94 },
        { name: "Amazon Redshift", level: 92 },
        { name: "Google BigQuery", level: 90 },
        { name: "Azure Synapse", level: 89 },
        { name: "Teradata", level: 85 },
      ],
    },
    {
      id: "bidashboards",
      title: "Business Intelligence Dashboards",
      icon: BarChart4,
      description:
        "Creating interactive, visually compelling dashboards to empower decision-making with real-time insights.",
      capabilities: [
        "Custom dashboard design",
        "KPI tracking & visualization",
        "Drill-down analytics",
        "Automated reporting",
        "Mobile dashboard access",
        "Data storytelling",
      ],
      technologies: [
        { name: "Tableau", level: 95 },
        { name: "Power BI", level: 93 },
        { name: "Looker", level: 91 },
        { name: "Qlik Sense", level: 88 },
        { name: "D3.js", level: 86 },
      ],
    },
    {
      id: "etl",
      title: "ETL Pipeline Development",
      icon: Network,
      description:
        "Designing and implementing efficient Extract, Transform, Load (ETL) pipelines to streamline data workflows.",
      capabilities: [
        "Data extraction from multiple sources",
        "Data cleansing & transformation",
        "Incremental loading",
        "Error handling & logging",
        "Pipeline orchestration",
        "Scalable ETL architecture",
      ],
      technologies: [
        { name: "Apache Airflow", level: 93 },
        { name: "Talend", level: 90 },
        { name: "Informatica", level: 89 },
        { name: "dbt", level: 92 },
        { name: "Pentaho", level: 87 },
      ],
    },
    {
      id: "realtime",
      title: "Real-time Analytics",
      icon: Clock,
      description:
        "Delivering real-time data processing and analytics capabilities to drive immediate business decisions.",
      capabilities: [
        "Stream processing setup",
        "Low-latency analytics",
        "Event-driven architecture",
        "Real-time dashboards",
        "Anomaly detection in streams",
        "Scalable stream ingestion",
      ],
      technologies: [
        { name: "Apache Kafka", level: 94 },
        { name: "Apache Flink", level: 91 },
        { name: "Spark Streaming", level: 90 },
        { name: "Kinesis", level: 89 },
        { name: "Storm", level: 86 },
      ],
    },
    {
      id: "datastrategy",
      title: "Data Strategy Consulting",
      icon: FileSearch,
      description:
        "Providing expert guidance to align your data initiatives with business goals and maximize data value.",
      capabilities: [
        "Data maturity assessment",
        "Data governance framework",
        "Roadmap development",
        "Data monetization strategies",
        "Compliance & privacy planning",
        "Data culture transformation",
      ],
      technologies: [
        { name: "Data Governance Tools", level: 93 },
        { name: "Strategic Planning", level: 95 },
        { name: "Risk Assessment", level: 92 },
        { name: "GDPR/CCPA Compliance", level: 90 },
        { name: "Stakeholder Alignment", level: 94 },
      ],
    },
  ],
  web3: [
    {
      id: "smartcontracts",
      title: "Smart Contract Development",
      icon: Code,
      description:
        "Building secure, audited smart contracts to automate processes and ensure trust in decentralized applications.",
      capabilities: [
        "Smart contract design & coding",
        "Security audits & testing",
        "Gas optimization",
        "Cross-chain compatibility",
        "Upgradeable contracts",
        "Custom token standards",
      ],
      technologies: [
        { name: "Solidity", level: 94 },
        { name: "Rust", level: 90 },
        { name: "Vyper", level: 88 },
        { name: "Truffle", level: 92 },
        { name: "Hardhat", level: 91 },
      ],
    },
    {
      id: "dapps",
      title: "Decentralized Applications (dApps)",
      icon: Globe,
      description:
        "Creating fully decentralized applications that leverage blockchain for transparency, security, and user empowerment.",
      capabilities: [
        "dApp frontend development",
        "Blockchain integration",
        "Wallet connectivity",
        "Decentralized storage",
        "User authentication flows",
        "Smart contract interaction",
      ],
      technologies: [
        { name: "Web3.js", level: 93 },
        { name: "Ethers.js", level: 92 },
        { name: "React/Next.js", level: 94 },
        { name: "IPFS", level: 90 },
        { name: "Metamask", level: 89 },
      ],
    },
    {
      id: "tokenization",
      title: "Tokenization & NFT Platforms",
      icon: Coins,
      description:
        "Developing token-based ecosystems and NFT platforms for digital ownership, rewards, and asset management.",
      capabilities: [
        "ERC-20/ERC-721/ERC-1155 tokens",
        "NFT marketplace development",
        "Tokenomics design",
        "Minting & burning mechanisms",
        "Metadata management",
        "Royalty implementation",
      ],
      technologies: [
        { name: "OpenZeppelin", level: 94 },
        { name: "Solidity", level: 93 },
        { name: "Polygon", level: 90 },
        { name: "Solana", level: 88 },
        { name: "Alchemy", level: 91 },
      ],
    },
    {
      id: "blockchainintegration",
      title: "Blockchain Integration",
      icon: Link2,
      description:
        "Integrating blockchain technology into existing systems to enhance security, transparency, and efficiency.",
      capabilities: [
        "Legacy system integration",
        "Cross-chain bridge development",
        "Oracle implementation",
        "API development for blockchain",
        "Private blockchain setup",
        "Hybrid blockchain solutions",
      ],
      technologies: [
        { name: "Chainlink", level: 92 },
        { name: "Hyperledger", level: 90 },
        { name: "Ethereum", level: 94 },
        { name: "Corda", level: 87 },
        { name: "Polkadot", level: 89 },
      ],
    },
    {
      id: "web3frontend",
      title: "Web3 Frontend Development",
      icon: Zap,
      description:
        "Crafting intuitive, blockchain-connected user interfaces for seamless interaction with decentralized ecosystems.",
      capabilities: [
        "Wallet integration",
        "Transaction handling UI",
        "Real-time blockchain data display",
        "Responsive design for Web3",
        "Decentralized identity support",
        "User onboarding flows",
      ],
      technologies: [
        { name: "React/Next.js", level: 95 },
        { name: "Web3.js", level: 93 },
        { name: "Ethers.js", level: 92 },
        { name: "TailwindCSS", level: 94 },
        { name: "Wagmi", level: 90 },
      ],
    },
    {
      id: "dao",
      title: "DAO Infrastructure",
      icon: Users,
      description:
        "Building decentralized autonomous organization (DAO) frameworks for transparent, community-driven governance.",
      capabilities: [
        "DAO smart contract development",
        "Voting system implementation",
        "Treasury management",
        "Proposal creation workflows",
        "Member onboarding",
        "Governance token integration",
      ],
      technologies: [
        { name: "Aragon", level: 92 },
        { name: "Snapshot", level: 90 },
        { name: "Gnosis Safe", level: 91 },
        { name: "Solidity", level: 94 },
        { name: "DAOstack", level: 88 },
      ],
    },
  ],
  security: [
    {
      id: "appsecurity",
      title: "Application Security Testing",
      icon: Shield,
      description:
        "Identifying and mitigating vulnerabilities in applications through comprehensive security testing methodologies.",
      capabilities: [
        "Static application security testing (SAST)",
        "Dynamic application security testing (DAST)",
        "Code vulnerability scanning",
        "Dependency analysis",
        "Secure coding practices",
        "Remediation guidance",
      ],
      technologies: [
        { name: "OWASP ZAP", level: 92 },
        { name: "Burp Suite", level: 94 },
        { name: "SonarQube", level: 90 },
        { name: "Snyk", level: 91 },
        { name: "Checkmarx", level: 88 },
      ],
    },
    {
      id: "pentesting",
      title: "Penetration Testing",
      icon: Lock,
      description:
        "Simulating real-world attacks to uncover weaknesses in systems, networks, and applications before malicious actors do.",
      capabilities: [
        "Network penetration testing",
        "Web application testing",
        "Mobile app penetration testing",
        "Social engineering tests",
        "Red team exercises",
        "Detailed reporting",
      ],
      technologies: [
        { name: "Metasploit", level: 93 },
        { name: "Kali Linux", level: 95 },
        { name: "Nmap", level: 92 },
        { name: "Wireshark", level: 90 },
        { name: "Cobalt Strike", level: 88 },
      ],
    },
    {
      id: "securityarchitecture",
      title: "Security Architecture Design",
      icon: DraftingCompass,
      description:
        "Designing secure system architectures to protect against threats and ensure resilience across all layers.",
      capabilities: [
        "Threat modeling",
        "Secure-by-design principles",
        "Encryption strategy",
        "Access control frameworks",
        "Network security design",
        "Zero trust architecture",
      ],
      technologies: [
        { name: "AWS Security", level: 93 },
        { name: "Azure Security", level: 91 },
        { name: "CIS Benchmarks", level: 90 },
        { name: "OpenSSL", level: 89 },
        { name: "HashiCorp Vault", level: 92 },
      ],
    },
    {
      id: "compliance",
      title: "Compliance & Audit Support",
      icon: FileCheck,
      description:
        "Ensuring your systems meet industry standards and regulatory requirements through audits and compliance frameworks.",
      capabilities: [
        "GDPR/CCPA compliance",
        "ISO 27001 implementation",
        "SOC 2 audits",
        "HIPAA compliance",
        "Gap analysis",
        "Policy documentation",
      ],
      technologies: [
        { name: "AuditBoard", level: 91 },
        { name: "Vanta", level: 93 },
        { name: "Nessus", level: 90 },
        { name: "ISO Standards", level: 94 },
        { name: "Compliance Frameworks", level: 92 },
      ],
    },
    {
      id: "devsecops",
      title: "DevSecOps Implementation",
      icon: GitBranch,
      description:
        "Integrating security into every stage of the DevOps pipeline to deliver secure software faster.",
      capabilities: [
        "Security automation in CI/CD",
        "Container security",
        "Infrastructure as Code security",
        "Secrets management",
        "Vulnerability scanning",
        "Secure pipeline design",
      ],
      technologies: [
        { name: "Docker Security", level: 92 },
        { name: "Kubernetes Security", level: 91 },
        { name: "GitLab Security", level: 90 },
        { name: "HashiCorp Vault", level: 93 },
        { name: "Aqua Security", level: 89 },
      ],
    },
    {
      id: "incidentresponse",
      title: "Incident Response Planning",
      icon: AlertTriangle,
      description:
        "Preparing and executing rapid response strategies to mitigate security incidents and minimize damage.",
      capabilities: [
        "Incident response plan development",
        "Threat hunting",
        "Forensic analysis",
        "Breach containment",
        "Post-incident review",
        "Crisis communication",
      ],
      technologies: [
        { name: "Splunk", level: 93 },
        { name: "Elastic Security", level: 91 },
        { name: "TheHive", level: 90 },
        { name: "FireEye", level: 88 },
        { name: "CrowdStrike", level: 94 },
      ],
    },
  ],
};

// Success metrics data
const successMetrics = [
  {
    title: "Customer Satisfaction",
    value: "96%",
    icon: Users,
    color: "bg-blue-500",
  },
  {
    title: "On-Time Delivery",
    value: "98.5%",
    icon: Clock,
    color: "bg-indigo-500",
  },
  {
    title: "Project Success Rate",
    value: "94%",
    icon: CheckCircle,
    color: "bg-emerald-500",
  },
  {
    title: "Performance Improvement",
    value: "3.2x",
    icon: Zap,
    color: "bg-amber-500",
  },
];

// Process steps
const processSteps = [
  {
    title: "Discovery & Assessment",
    description:
      "We begin by deeply understanding your business goals, challenges, and requirements.",
    icon: FileSearch,
  },
  {
    title: "Strategic Planning",
    description:
      "Our experts develop a tailored solution strategy aligned with your business objectives.",
    icon: BarChart4,
  },
  {
    title: "Agile Implementation",
    description:
      "Using agile methodologies, we deliver value quickly with frequent iterations and feedback loops.",
    icon: RefreshCcw,
  },
  {
    title: "Testing & Optimization",
    description:
      "Rigorous testing ensures quality, performance, and security at every stage.",
    icon: Shield,
  },
  {
    title: "Deployment & Integration",
    description:
      "Seamless deployment with comprehensive integration into your existing systems.",
    icon: CloudCog,
  },
  {
    title: "Support & Evolution",
    description:
      "Ongoing support, maintenance, and continuous improvement to evolve with your needs.",
    icon: Zap,
  },
];

// FAQ items
const faqItems = [
  {
    question: "How do you ensure the quality of your solutions?",
    answer:
      "We maintain stringent quality standards through comprehensive testing protocols, code reviews, and continuous integration. Our solutions undergo security audits, performance testing, and user acceptance testing before delivery. Post-launch, we provide monitoring and support to ensure continued quality.",
  },
  {
    question: "What technologies do you specialize in?",
    answer:
      "Our expertise spans modern web frameworks (React, Next.js, Vue), backend technologies (Node.js, Python, .NET), cloud platforms (AWS, Azure, GCP), mobile development (React Native, Flutter), AI/ML (TensorFlow, PyTorch), and data engineering tools. We select the best technology stack based on your specific requirements and long-term goals.",
  },
  {
    question: "How long does a typical project take to complete?",
    answer:
      "Project timelines vary based on scope, complexity, and requirements. An MVP might take 4-8 weeks, while enterprise solutions may span 3-6 months or longer. We work with agile methodologies to deliver value incrementally, providing transparency throughout the process and regular deployments of working features.",
  },
  {
    question: "Do you offer support after the solution is deployed?",
    answer:
      "Absolutely. We offer various post-deployment support options including maintenance plans, 24/7 monitoring, regular updates, performance optimization, and feature enhancements. Our support packages can be tailored to your specific needs, ensuring your solution continues to perform optimally and evolve with your business.",
  },
  {
    question: "How do you handle data security and privacy?",
    answer:
      "We implement industry best practices for security at every level: secure coding standards, encryption for data at rest and in transit, rigorous access controls, and regular security audits. We design solutions with privacy by design principles and ensure compliance with relevant regulations like GDPR, HIPAA, or CCPA as required.",
  },
];

// Component for animated section header
const SectionHeader = ({ title, subtitle, align = "center" }) => {
  const ref = useRef(null);
  const isInView = useInView(ref, { once: true, amount: 0.3 });

  return (
    <motion.div
      ref={ref}
      initial={{ opacity: 0, y: 20 }}
      animate={isInView ? { opacity: 1, y: 0 } : { opacity: 0, y: 20 }}
      transition={{ duration: 0.7 }}
      className={`mb-16 ${align === "center" ? "text-center" : "text-left"}`}
    >
      <h2 className="text-3xl md:text-4xl font-bold mb-4 bg-clip-text text-transparent bg-gradient-to-r from-white to-gray-400">
        {title}
      </h2>
      <p className="text-lg text-gray-400 max-w-3xl mx-auto">{subtitle}</p>
    </motion.div>
  );
};

// Feature card component
const FeatureCard = ({ feature, index, color }) => {
  const ref = useRef(null);
  const isInView = useInView(ref, { once: true, amount: 0.5 });

  return (
    <motion.div
      ref={ref}
      initial={{ opacity: 0, y: 20 }}
      animate={isInView ? { opacity: 1, y: 0 } : { opacity: 0, y: 20 }}
      transition={{ duration: 0.5, delay: index * 0.1 }}
      className={`group rounded-xl border border-${color}-500/20 bg-gradient-to-br from-background to-${color}-950/10 backdrop-blur-sm p-4 hover:shadow-lg hover:shadow-${color}-500/5 transition-all duration-300`}
    >
      <div className="flex items-start gap-3">
        <div
          className={`flex-shrink-0 mt-0.5 p-1.5 rounded-full bg-${color}-500/10 text-${color}-500`}
        >
          <CheckCircle2 className="h-4 w-4" />
        </div>
        <span className="text-sm font-medium">{feature}</span>
      </div>
    </motion.div>
  );
};

// Technology skill bar component
const TechSkillBar = ({ tech, index }) => {
  const ref = useRef(null);
  const isInView = useInView(ref, { once: true, amount: 0.5 });

  return (
    <motion.div
      ref={ref}
      initial={{ opacity: 0, x: -20 }}
      animate={isInView ? { opacity: 1, x: 0 } : { opacity: 0, x: -20 }}
      transition={{ duration: 0.4, delay: index * 0.1 }}
      className="mb-3 last:mb-0"
    >
      <div className="flex justify-between items-center mb-1">
        <span className="text-sm font-medium text-gray-300">{tech.name}</span>
        <span className="text-xs text-gray-400">{tech.level}%</span>
      </div>
      <Progress
        value={tech.level}
        className="h-1.5 bg-gray-700 bg-gradient-to-r from-blue-500 to-cyan-400"
      />
    </motion.div>
  );
};

// Individual solution content component
const SolutionContent = ({ solution }) => {
  const ref = useRef(null);
  const isInView = useInView(ref, { once: true, amount: 0.1 });

  if (!solution) return null;

  return (
    <motion.div
      ref={ref}
      initial={{ opacity: 0 }}
      animate={isInView ? { opacity: 1 } : { opacity: 0 }}
      transition={{ duration: 0.6 }}
      className="space-y-8"
    >
      <div className="grid md:grid-cols-2 gap-6">
        <div className="space-y-4">
          <div className="flex items-center gap-3">
            <div className="p-2 rounded-lg bg-slate-800/50 border border-slate-700/50">
              <solution.icon className="h-5 w-5 text-blue-400" />
            </div>
            <h3 className="text-xl font-semibold">{solution.title}</h3>
          </div>
          <p className="text-gray-400">{solution.description}</p>

          <div className="mt-6">
            <h4 className="text-sm font-medium text-gray-300 mb-3">
              Core Capabilities
            </h4>
            <ul className="space-y-2">
              {solution.capabilities.map((capability, idx) => (
                <li key={idx} className="flex items-center gap-2">
                  <CheckCircle2 className="h-4 w-4 text-blue-500 flex-shrink-0" />
                  <span className="text-sm text-gray-400">{capability}</span>
                </li>
              ))}
            </ul>
          </div>
        </div>

        <div className="bg-slate-900/50 rounded-xl border border-slate-800/50 p-6">
          <h4 className="text-sm font-medium text-gray-300 mb-4">
            Technology Proficiency
          </h4>
          <div className="space-y-4">
            {solution.technologies.map((tech, idx) => (
              <TechSkillBar key={idx} tech={tech} index={idx} />
            ))}
          </div>
        </div>
      </div>
    </motion.div>
  );
};

// Process step card component
const ProcessStep = ({ step, index }) => {
  const ref = useRef(null);
  const isInView = useInView(ref, { once: true, amount: 0.5 });

  return (
    <motion.div
      ref={ref}
      initial={{ opacity: 0, y: 30 }}
      animate={isInView ? { opacity: 1, y: 0 } : { opacity: 0, y: 30 }}
      transition={{ duration: 0.5, delay: index * 0.15 }}
      className="relative"
    >
      {/* Connector line */}
      {index < processSteps.length - 1 && (
        <div className="absolute top-9 left-9 h-full w-0.5 bg-gradient-to-b from-blue-500/80 to-blue-500/20 z-0" />
      )}

      <div className="flex items-start gap-6 relative z-10">
        <div className="flex-shrink-0 w-18 h-18 rounded-full bg-gradient-to-br from-blue-600 to-cyan-600 p-4 flex items-center justify-center">
          <step.icon className="h-8 w-8 text-white" />
        </div>
        <div className="pt-2">
          <h3 className="text-xl font-semibold mb-2">{step.title}</h3>
          <p className="text-gray-400">{step.description}</p>
        </div>
      </div>
    </motion.div>
  );
};

// FAQ item component
const FaqItem = ({ item, index, isOpen, toggle }) => {
  const contentRef = useRef(null);
  const isInView = useInView(contentRef, { once: true, amount: 0.1 });

  return (
    <motion.div
      ref={contentRef}
      initial={{ opacity: 0, y: 20 }}
      animate={isInView ? { opacity: 1, y: 0 } : { opacity: 0, y: 20 }}
      transition={{ duration: 0.4, delay: index * 0.1 }}
      className="border-b border-slate-800 last:border-0"
    >
      <button
        onClick={() => toggle(index)}
        className="flex justify-between items-center w-full py-5 text-left"
      >
        <span className="font-medium text-lg">{item.question}</span>
        <ChevronRight
          className={`h-5 w-5 text-gray-400 transition-transform duration-300 ${
            isOpen === index ? "rotate-90" : ""
          }`}
        />
      </button>
      <AnimatePresence>
        {isOpen === index && (
          <motion.div
            initial={{ height: 0, opacity: 0 }}
            animate={{ height: "auto", opacity: 1 }}
            exit={{ height: 0, opacity: 0 }}
            transition={{ duration: 0.3 }}
            className="overflow-hidden"
          >
            <p className="pb-5 text-gray-400">{item.answer}</p>
          </motion.div>
        )}
      </AnimatePresence>
    </motion.div>
  );
};

// Call to action banner component
const CtaBanner = () => {
  const ref = useRef(null);
  const isInView = useInView(ref, { once: true, amount: 0.3 });

  return (
    <motion.div
      ref={ref}
      initial={{ opacity: 0, y: 40 }}
      animate={isInView ? { opacity: 1, y: 0 } : { opacity: 0, y: 40 }}
      transition={{ duration: 0.7 }}
      className="relative rounded-2xl overflow-hidden"
    >
      {/* Background gradient */}
      <div className="absolute inset-0 bg-gradient-to-br from-blue-700/50 via-indigo-700/30 to-violet-700/50 z-0" />

      {/* Mesh grid */}
      <div className="absolute inset-0 bg-[linear-gradient(to_right,#ffffff06_1px,transparent_1px),linear-gradient(to_bottom,#ffffff06_1px,transparent_1px)] bg-[size:24px_24px] z-0" />

      {/* Glowing orbs */}
      <div className="absolute top-0 left-1/4 w-64 h-64 bg-blue-500/20 rounded-full filter blur-3xl" />
      <div className="absolute bottom-0 right-1/4 w-96 h-96 bg-violet-500/20 rounded-full filter blur-3xl" />

      <div className="relative z-10 px-8 py-16 md:py-20 text-center">
        <h3 className="text-3xl md:text-4xl font-bold mb-6 max-w-2xl mx-auto">
          Ready to Transform Your Business with Our Solutions?
        </h3>
        <p className="text-lg text-gray-300 mb-8 max-w-2xl mx-auto">
          Let&apos;s discuss how our expertise can help you overcome challenges
          and achieve your goals.
        </p>
        <div className="flex flex-col sm:flex-row justify-center gap-4">
          <Button
            size="lg"
            className="bg-gradient-to-r from-blue-600 to-cyan-600 hover:opacity-90 text-white border-0 shadow-lg shadow-blue-500/20"
          >
            Book a Consultation
          </Button>
          <Button
            size="lg"
            variant="outline"
            className="border-white/20 bg-white/10 hover:bg-white/20 text-white"
          >
            View Case Studies
          </Button>
        </div>
      </div>
    </motion.div>
  );
};

// Main component
const SolutionsPage = () => {
  const [activeTab, setActiveTab] = useState("tech");
  const [activeDetailItem, setActiveDetailItem] = useState("fullstack");
  const [activeFaq, setActiveFaq] = useState(null);
  const ref = useRef(null);

  // Getting active solution category
  const activeCategory = solutionCategories.find((cat) => cat.id === activeTab);

  // Getting solutions for active category
  const activeSolutions = expandedSolutions[activeTab] || [];

  // Getting active solution detail
  const activeSolution =
    activeSolutions.find((sol) => sol.id === activeDetailItem) ||
    activeSolutions[0];

  // Toggle FAQ
  const toggleFaq = (index) => {
    setActiveFaq(activeFaq === index ? null : index);
  };

  // Update active detail item when changing tabs
  useEffect(() => {
    if (expandedSolutions[activeTab]?.length > 0) {
      setActiveDetailItem(expandedSolutions[activeTab][0].id);
    }
  }, [activeTab]);

  return (
    <div className="min-h-screen bg-slate-950 text-white">
      {/* Hero section */}
      <section className="relative overflow-hidden pt-32 pb-24">
        {/* Background elements */}
        <div className="absolute inset-0 bg-[radial-gradient(ellipse_at_center,var(--tw-gradient-stops))] from-blue-950/30 via-slate-950 to-slate-950 z-0" />

        {/* Animated particles */}
        <div className="absolute inset-0 overflow-hidden">
          {Array.from({ length: 30 }).map((_, i) => (
            <motion.div
              key={i}
              className={`absolute rounded-full ${
                i % 5 === 0
                  ? "bg-blue-500"
                  : i % 5 === 1
                  ? "bg-indigo-500"
                  : i % 5 === 2
                  ? "bg-emerald-500"
                  : i % 5 === 3
                  ? "bg-amber-500"
                  : "bg-violet-500"
              } opacity-10`}
              style={{
                width: Math.random() * 50 + 10 + "px",
                height: Math.random() * 50 + 10 + "px",
                left: Math.random() * 100 + "%",
                top: Math.random() * 100 + "%",
              }}
              animate={{
                y: [Math.random() * 100, Math.random() * -100],
                x: [Math.random() * 100, Math.random() * -100],
                opacity: [0.05, 0.2, 0.05],
              }}
              transition={{
                duration: Math.random() * 20 + 15,
                repeat: Infinity,
                ease: "easeInOut",
              }}
            />
          ))}
        </div>

        {/* Hero content */}
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 relative z-10">
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8 }}
            className="text-center max-w-3xl mx-auto mb-16"
          >
            <Badge
              variant="outline"
              className="px-4 py-1.5 mb-6 bg-slate-900/50 text-blue-400 border-blue-500/20 rounded-full backdrop-blur-sm"
            >
              Comprehensive Solutions
            </Badge>
            <h1 className="text-5xl md:text-6xl lg:text-7xl font-bold mb-6 leading-tight">
              Transforming{" "}
              <span className="bg-clip-text text-transparent bg-gradient-to-r from-blue-400 to-cyan-400">
                Ideas
              </span>{" "}
              into{" "}
              <span className="bg-clip-text text-transparent bg-gradient-to-r from-indigo-400 to-violet-400">
                Reality
              </span>
            </h1>
            <p className="text-xl text-gray-300 mb-8">
              End-to-end solutions tailored to your business needs, from concept
              to implementation and beyond.
            </p>
            <Link href="/contact">
              <div className="flex flex-col sm:flex-row justify-center gap-4">
                <Button
                  size="lg"
                  variant="outline"
                  className="border-white/20 bg-white/5 hover:bg-white/10 text-white"
                >
                  Contact Us
                </Button>
              </div>
            </Link>
          </motion.div>

          {/* Success metrics */}
          <motion.div
            initial={{ opacity: 0, y: 40 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8, delay: 0.2 }}
          >
            <div className="grid grid-cols-2 md:grid-cols-4 gap-4 md:gap-6">
              {successMetrics.map((metric, idx) => (
                <Card
                  key={idx}
                  className="bg-slate-900/60 border-slate-800/60 backdrop-blur-lg overflow-hidden"
                >
                  <CardContent className="p-6">
                    <div className="flex items-center gap-4">
                      <div
                        className={`p-3 rounded-lg ${metric.color} bg-opacity-20`}
                      >
                        <metric.icon
                          className={`h-6 w-6 ${metric.color} text-opacity-90`}
                        />
                      </div>
                      <div>
                        <div className="text-2xl font-bold">{metric.value}</div>
                        <div className="text-xs text-gray-400">
                          {metric.title}
                        </div>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              ))}
            </div>
          </motion.div>
        </div>
      </section>

      {/* Main solutions section */}
      <section id="solutions" className="relative py-24">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <SectionHeader
            title="Our Solutions Portfolio"
            subtitle="Comprehensive solutions designed to address your unique challenges and drive tangible business outcomes."
          />

          <Tabs
            defaultValue="tech"
            className="w-full"
            onValueChange={setActiveTab}
            value={activeTab}
          >
            {/* Solution category tabs */}
            <div className="relative flex justify-center mb-12">
              <ScrollArea className="w-full pb-6">
                <div className="flex justify-center">
                  <TabsList className="h-16 rounded-lg bg-slate-900/50 backdrop-blur-md border border-white/5 p-1.5">
                    {solutionCategories.map((solution) => (
                      <TabsTrigger
                        key={solution.id}
                        value={solution.id}
                        className={`px-6 h-full text-base transition-all duration-300 data-[state=active]:${solution.gradientFrom} data-[state=active]:${solution.gradientTo} data-[state=active]:text-white data-[state=active]:shadow-lg rounded-md`}
                      >
                        <motion.div
                          className="flex items-center gap-2.5"
                          whileTap={{ scale: 0.97 }}
                        >
                          <solution.icon className="h-5 w-5" />
                          <span className="font-medium">{solution.title}</span>
                        </motion.div>
                      </TabsTrigger>
                    ))}
                  </TabsList>
                </div>
                <ScrollBar orientation="horizontal" className="hidden" />
              </ScrollArea>
            </div>

            {/* Category overview cards */}
            <div className="mb-16">
              <AnimatePresence mode="wait">
                {solutionCategories.map(
                  (solution) =>
                    solution.id === activeTab && (
                      <TabsContent
                        key={solution.id}
                        value={solution.id}
                        className="mt-0"
                      >
                        <motion.div
                          initial={{ opacity: 0, y: 20 }}
                          animate={{ opacity: 1, y: 0 }}
                          exit={{ opacity: 0, y: -20 }}
                          transition={{ duration: 0.5 }}
                        >
                          <Card className="bg-gradient-to-br from-slate-900/60 to-slate-950/80 backdrop-blur-lg border border-white/10 shadow-2xl rounded-2xl overflow-hidden">
                            <CardContent className="p-0">
                              {/* Gradient accent line at top */}
                              <div
                                className={`h-1.5 w-full bg-gradient-to-r ${solution.gradientFrom} ${solution.gradientTo}`}
                              />

                              <div className="p-8 md:p-10">
                                <div className="grid lg:grid-cols-2 gap-12 items-start">
                                  <div>
                                    <motion.div
                                      initial={{ opacity: 0, y: 20 }}
                                      animate={{ opacity: 1, y: 0 }}
                                      transition={{ duration: 0.6, delay: 0.1 }}
                                    >
                                      <div
                                        className={`inline-flex items-center justify-center h-14 w-14 rounded-xl bg-gradient-to-br ${solution.gradientFrom} ${solution.gradientTo} mb-6 shadow-lg shadow-${solution.color}-500/20`}
                                      >
                                        <solution.icon className="h-7 w-7 text-white" />
                                      </div>

                                      <h3
                                        className={`text-3xl font-bold mb-3 bg-clip-text text-transparent bg-gradient-to-r ${solution.gradientFrom} ${solution.gradientTo}`}
                                      >
                                        {solution.title}
                                      </h3>
                                      <p className="text-lg text-gray-400 mb-6">
                                        {solution.description}
                                      </p>

                                      <div className="grid grid-cols-1 md:grid-cols-2 gap-3">
                                        {solution.features.map(
                                          (feature, idx) => (
                                            <FeatureCard
                                              key={idx}
                                              feature={feature}
                                              index={idx}
                                              color={solution.color}
                                            />
                                          )
                                        )}
                                      </div>
                                    </motion.div>
                                  </div>

                                  {/* Stats and CTA */}
                                  <div className="space-y-8 lg:pl-12">
                                    <motion.div
                                      initial={{ opacity: 0, x: 20 }}
                                      animate={{ opacity: 1, x: 0 }}
                                      transition={{ duration: 0.6, delay: 0.2 }}
                                      className="flex items-center justify-center bg-slate-900/50 rounded-xl p-6 border border-slate-800/50"
                                    >
                                      <div className="flex items-center gap-4">
                                        <div
                                          className={`p-3 rounded-lg bg-${solution.color}-500/10`}
                                        >
                                          <Award
                                            className={`h-6 w-6 text-${solution.color}-500`}
                                          />
                                        </div>
                                        <div>
                                          <div className="text-3xl font-bold">
                                            {solution.stat.value}
                                          </div>
                                          <div className="text-sm text-gray-400">
                                            {solution.stat.label}
                                          </div>
                                        </div>
                                      </div>
                                    </motion.div>

                                    <motion.div
                                      initial={{ opacity: 0, y: 20 }}
                                      animate={{ opacity: 1, y: 0 }}
                                      transition={{ duration: 0.6, delay: 0.3 }}
                                      className="bg-slate-900/50 rounded-xl p-6 border border-slate-800/50"
                                    >
                                      <h4 className="text-sm font-medium text-gray-300 mb-4">
                                        Why Choose Us?
                                      </h4>
                                      <ul className="space-y-3">
                                        <li className="flex items-center gap-2 text-gray-400">
                                          <CheckCircle2 className="h-4 w-4 text-emerald-500" />
                                          <span>
                                            Industry-leading expertise
                                          </span>
                                        </li>
                                        <li className="flex items-center gap-2 text-gray-400">
                                          <CheckCircle2 className="h-4 w-4 text-emerald-500" />
                                          <span>Proven track record</span>
                                        </li>
                                        <li className="flex items-center gap-2 text-gray-400">
                                          <CheckCircle2 className="h-4 w-4 text-emerald-500" />
                                          <span>Agile development process</span>
                                        </li>
                                      </ul>
                                    </motion.div>
                                  </div>
                                </div>
                              </div>
                            </CardContent>
                          </Card>
                        </motion.div>
                      </TabsContent>
                    )
                )}
              </AnimatePresence>
            </div>

            {/* Detailed solutions navigation */}
            <ScrollArea className="w-full pb-6 mb-12">
              <div className="flex gap-2">
                {activeSolutions.map((solution) => (
                  <Button
                    key={solution.id}
                    onClick={() => setActiveDetailItem(solution.id)}
                    variant={
                      activeDetailItem === solution.id ? "default" : "outline"
                    }
                    className={`rounded-full ${
                      activeDetailItem === solution.id
                        ? `bg-gradient-to-r ${activeCategory.gradientFrom} ${activeCategory.gradientTo} text-white`
                        : "bg-slate-900/50 border-slate-700/50 hover:bg-slate-800/50"
                    }`}
                  >
                    <solution.icon className="h-4 w-4 mr-2" />
                    {solution.title}
                  </Button>
                ))}
              </div>
              <ScrollBar orientation="horizontal" />
            </ScrollArea>

            {/* Detailed solution content */}
            <SolutionContent solution={activeSolution} />
          </Tabs>
        </div>
      </section>

      {/* Process section */}
      <section className="py-24 bg-gradient-to-b from-slate-950 to-slate-900/50">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <SectionHeader
            title="Our Development Process"
            subtitle="A structured approach that ensures quality, transparency, and successful delivery at every stage."
          />

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
            {processSteps.map((step, index) => (
              <ProcessStep key={index} step={step} index={index} />
            ))}
          </div>
        </div>
      </section>

      {/* FAQ section */}
      <section className="py-24">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <SectionHeader
            title="Frequently Asked Questions"
            subtitle="Find answers to common questions about our solutions and services."
          />

          <div className="max-w-3xl mx-auto">
            {faqItems.map((item, index) => (
              <FaqItem
                key={index}
                item={item}
                index={index}
                isOpen={activeFaq}
                toggle={toggleFaq}
              />
            ))}
          </div>
        </div>
      </section>

      {/* CTA section */}
      <section className="py-24">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <CtaBanner />
        </div>
      </section>
    </div>
  );
};

export default SolutionsPage;
