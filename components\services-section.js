"use client";

import { useRef, useEffect } from "react";
import { Card, CardContent } from "@/components/ui/card";
import { Brain, Code, LineChart, Lightbulb, Rocket, Users } from "lucide-react";
import { motion, useScroll, useTransform } from "framer-motion";
import Link from "next/link";

const services = [
  {
    icon: Lightbulb,
    title: "Business Strategy",
    description:
      "Develop comprehensive business plans, market analysis, and growth strategies tailored to your unique goals and challenges.",
  },
  {
    icon: Brain,
    title: "AI & ML Solutions",
    description:
      "Leverage cutting-edge artificial intelligence and machine learning technologies to automate processes and gain valuable insights.",
  },
  {
    icon: Code,
    title: "Web Development",
    description:
      "Create stunning, responsive websites and web applications that deliver exceptional user experiences and drive conversions.",
  },
  {
    icon: Rocket,
    title: "Software Development",
    description:
      "Build custom software solutions that streamline operations, enhance productivity, and solve complex business problems.",
  },
  {
    icon: LineChart,
    title: "Data Analytics",
    description:
      "Transform raw data into actionable insights with advanced analytics tools and visualization techniques.",
  },
  {
    icon: Users,
    title: "Digital Transformation",
    description:
      "Guide your organization through digital transformation with strategic planning and implementation of innovative technologies.",
  },
];

// Star component for the background
const Star = ({ size, left, top, delay }) => {
  return (
    <motion.div
      className="absolute rounded-full bg-white"
      style={{
        width: size,
        height: size,
        left: `${left}%`,
        top: `${top}%`,
      }}
      initial={{ opacity: 0.1, scale: 0.8 }}
      animate={{
        opacity: [0.1, 0.8, 0.1],
        scale: [0.8, 1.2, 0.8],
        boxShadow: [
          "0 0 2px rgba(255, 255, 255, 0.5)",
          "0 0 10px rgba(255, 255, 255, 0.8), 0 0 20px rgba(255, 255, 255, 0.4)",
          "0 0 2px rgba(255, 255, 255, 0.5)",
        ],
      }}
      transition={{
        repeat: Infinity,
        duration: 3 + Math.random() * 3,
        delay: delay,
        ease: "easeInOut",
      }}
    />
  );
};

const ServicesSection = () => {
  const containerRef = useRef(null);
  const { scrollYProgress } = useScroll({
    target: containerRef,
    offset: ["start end", "end start"],
  });

  // Parallax effects
  const y1 = useTransform(scrollYProgress, [0, 1], [0, -100]);
  const y2 = useTransform(scrollYProgress, [0, 1], [0, -150]);
  const y3 = useTransform(scrollYProgress, [0, 1], [0, -50]);

  // Generate stars data
  const starsData = Array.from({ length: 40 }, (_, i) => ({
    id: i,
    size: 1 + Math.random() * 3,
    left: Math.random() * 100,
    top: Math.random() * 100,
    delay: Math.random() * 2,
  }));

  return (
    <section
      id="services"
      className="py-24 relative overflow-hidden"
      ref={containerRef}
    >
      {/* Preserve the original gradient background */}
      <div className="absolute inset-0 bg-gradient-to-b from-background to-background/95 z-0" />

      {/* Stars background */}
      <div className="absolute inset-0 z-0 opacity-70">
        {starsData.map((star) => (
          <Star
            key={star.id}
            size={star.size}
            left={star.left}
            top={star.top}
            delay={star.delay}
          />
        ))}
      </div>

      {/* Content container */}
      <div className="max-w-7xl mx-auto px-6 lg:px-8 relative z-10">
        {/* Section header with parallax */}
        <motion.div
          className="text-center mb-20"
          initial={{ opacity: 0, y: 20 }}
          whileInView={{ opacity: 1, y: 0 }}
          viewport={{ once: true, margin: "-100px" }}
          transition={{ duration: 0.6 }}
          style={{ y: y1 }}
        >
          <motion.div
            className="inline-block mb-3"
            initial={{ opacity: 0, scale: 0.8 }}
            whileInView={{ opacity: 1, scale: 1 }}
            viewport={{ once: true }}
            transition={{ duration: 0.5, delay: 0.2 }}
          >
            <div className="px-4 py-1 rounded-full bg-purple-500/10 border border-purple-500/20 text-purple-400 text-sm font-medium">
              Our Expertise
            </div>
          </motion.div>

          <motion.h2
            className="text-3xl md:text-5xl font-bold mb-6 bg-clip-text text-transparent bg-gradient-to-r from-purple-400 to-indigo-600"
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            viewport={{ once: true }}
            transition={{ duration: 0.5, delay: 0.3 }}
          >
            Comprehensive Services
          </motion.h2>

          <motion.p
            className="text-xl text-gray-400 max-w-3xl mx-auto"
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            viewport={{ once: true }}
            transition={{ duration: 0.5, delay: 0.4 }}
          >
            Innovative solutions designed to help your business thrive in
            today&apos;s competitive landscape.
          </motion.p>
        </motion.div>

        {/* Services grid with parallax */}
        <motion.div
          className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8"
          style={{ y: y2 }}
        >
          {services.map((service, index) => {
            const IconComponent = service.icon;

            return (
              <motion.div
                key={index}
                initial={{ opacity: 0, y: 30 }}
                whileInView={{ opacity: 1, y: 0 }}
                viewport={{ once: true, margin: "-100px" }}
                transition={{
                  duration: 0.5,
                  delay: 0.1 + index * 0.1,
                }}
              >
                <motion.div
                  whileHover={{
                    y: -8,
                    transition: { duration: 0.3, ease: "easeOut" },
                  }}
                  className="h-full"
                >
                  <Card className="backdrop-blur-lg border border-white/10 bg-black/30 shadow-xl overflow-hidden h-full group relative">
                    <CardContent className="p-6 flex flex-col h-full">
                      {/* Card glow effect */}
                      <div className="absolute -inset-0.5 bg-gradient-to-r from-purple-500 to-indigo-600 rounded-xl opacity-0 group-hover:opacity-30 blur transition duration-500 group-hover:duration-200" />

                      {/* Icon wrapper */}
                      <motion.div
                        className="p-4 rounded-xl bg-gradient-to-br from-purple-500/10 to-purple-700/20 border border-purple-500/10 mb-6 relative"
                        whileHover={{
                          scale: 1.05,
                          transition: { duration: 0.2 },
                        }}
                      >
                        <div className="flex items-center justify-center bg-clip-text bg-gradient-to-r from-purple-400 to-indigo-500">
                          <IconComponent className="h-10 w-10" />
                        </div>
                      </motion.div>

                      {/* Content */}
                      <h3 className="text-xl font-bold mb-3 text-white group-hover:text-purple-300 transition-colors duration-300">
                        {service.title}
                      </h3>
                      <p className="text-gray-400 group-hover:text-gray-300 transition-colors duration-300">
                        {service.description}
                      </p>
                    </CardContent>
                  </Card>
                </motion.div>
              </motion.div>
            );
          })}
        </motion.div>

        {/* View All Services Button with parallax */}
        <motion.div
          className="mt-16 text-center"
          initial={{ opacity: 0, y: 20 }}
          whileInView={{ opacity: 1, y: 0 }}
          viewport={{ once: true }}
          transition={{ duration: 0.5, delay: 0.6 }}
          style={{ y: y3 }}
        >
          <Link href="/services">
            <motion.button
              className="px-8 py-3 rounded-full bg-gradient-to-r from-blue-600 via-purple-600 to-pink-600 hover:from-blue-700 hover:via-purple-700 hover:to-pink-700 text-white font-medium shadow-lg shadow-purple-500/20 hover:shadow-purple-500/40 transition-all duration-300"
              whileHover={{
                scale: 1.05,
                boxShadow: "0 10px 25px -5px rgba(168, 85, 247, 0.4)",
              }}
              whileTap={{ scale: 0.98 }}
            >
              View All Services
            </motion.button>
          </Link>
        </motion.div>
      </div>
    </section>
  );
};

export default ServicesSection;
