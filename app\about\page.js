import React from "react";
import { motion } from "framer-motion";
import { Card, CardContent } from "@/components/ui/card";
import { Tabs, <PERSON><PERSON>Content, <PERSON><PERSON>List, TabsTrigger } from "@/components/ui/tabs";
import Image from "next/image";

const AboutPage = () => {
  // Animation variants
  const fadeIn = {
    hidden: { opacity: 0, y: 20 },
    visible: {
      opacity: 1,
      y: 0,
      transition: { duration: 0.6 },
    },
  };

  const staggerContainer = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        staggerChildren: 0.2,
      },
    },
  };

  // Team members data by department
  const teamMembers = {
    leadership: [
      {
        name: "<PERSON><PERSON><PERSON>",
        role: "CEO & Founder",
        image: "/api/placeholder/300/300",
        bio: "With over 15 years of experience in tech leadership, <PERSON><PERSON><PERSON> founded OfStartup to bridge the gap between cutting-edge technology and practical business solutions. His vision drives our company's commitment to innovation.",
        linkedin: "#",
      },
      {
        name: "<PERSON><PERSON>",
        role: "CT<PERSON>",
        image: "/api/placeholder/300/300",
        bio: "<PERSON><PERSON> leads our technology strategy with expertise in AI/ML and enterprise architecture. Her background includes leadership roles at major tech companies and a PhD in Computer Science from IIT Delhi.",
        linkedin: "#",
      },
      {
        name: "Vikram Mehta",
        role: "COO",
        image: "/api/placeholder/300/300",
        bio: "Vikram oversees our global operations, ensuring seamless delivery and exceptional client experiences. His operational excellence comes from years at multinational consulting firms.",
        linkedin: "#",
      },
    ],
    engineering: [
      {
        name: "Neha Gupta",
        role: "Head of AI & ML",
        image: "/api/placeholder/300/300",
        bio: "Neha leads our AI/ML initiatives, translating complex algorithms into business value. Her team pioneers solutions in predictive analytics, NLP, and computer vision.",
        linkedin: "#",
      },
      {
        name: "Rajiv Kumar",
        role: "Lead Software Architect",
        image: "/api/placeholder/300/300",
        bio: "Rajiv designs scalable systems that power enterprise transformations. His expertise spans cloud infrastructure, microservices, and high-performance computing.",
        linkedin: "#",
      },
      {
        name: "Ananya Singh",
        role: "Web3 & Blockchain Lead",
        image: "/api/placeholder/300/300",
        bio: "Ananya drives our Web3 initiatives, bringing expertise in blockchain technologies, smart contracts, and decentralized applications to solve complex business challenges.",
        linkedin: "#",
      },
    ],
    business: [
      {
        name: "Deepak Verma",
        role: "Business Strategy Director",
        image: "/api/placeholder/300/300",
        bio: "Deepak translates technological capabilities into business strategies that drive growth. His consulting background helps clients navigate digital transformations.",
        linkedin: "#",
      },
      {
        name: "Sonia Reddy",
        role: "Data Analytics Lead",
        image: "/api/placeholder/300/300",
        bio: "Sonia turns data into actionable insights, helping organizations make informed decisions. Her team specializes in data visualization, business intelligence, and advanced analytics.",
        linkedin: "#",
      },
      {
        name: "Karan Malhotra",
        role: "Digital Marketing Head",
        image: "/api/placeholder/300/300",
        bio: "Karan leads our digital marketing initiatives, combining creativity with data-driven strategies to enhance brand presence and drive engagement for our clients.",
        linkedin: "#",
      },
    ],
  };

  // Company values
  const values = [
    {
      title: "Innovation First",
      description:
        "We embrace cutting-edge technologies and methodologies to solve complex business challenges.",
      icon: "💡",
    },
    {
      title: "Global Perspective",
      description:
        "Based in India with a global mindset, we create solutions that work across cultural and geographical boundaries.",
      icon: "🌏",
    },
    {
      title: "Client Partnership",
      description:
        "We measure our success by our clients' success, functioning as an extension of their team.",
      icon: "🤝",
    },
    {
      title: "Ethical Technology",
      description:
        "We develop solutions with integrity, prioritizing security, privacy, and responsible innovation.",
      icon: "🛡️",
    },
  ];

  return (
    <div className="bg-gray-950 text-gray-100 min-h-screen">
      {/* Hero Section */}
      <section className="relative overflow-hidden">
        <div className="absolute inset-0 z-0 bg-gradient-to-br from-blue-900 via-purple-900 to-gray-900 opacity-40"></div>
        <div className="absolute inset-0 z-0 bg-[url('/api/placeholder/1920/1080')] bg-cover opacity-10"></div>

        <div className="container mx-auto px-6 py-24 relative z-10">
          <div
            initial="hidden"
            animate="visible"
            variants={fadeIn}
            className="text-center max-w-3xl mx-auto"
          >
            <h1 className="text-5xl font-bold mb-6 bg-clip-text text-transparent bg-gradient-to-r from-blue-400 to-purple-500">
              Meet the Team behind OfStartup
            </h1>
            <p className="text-xl text-gray-300 mb-8">
              Innovators, strategists, and tech enthusiasts united by a passion
              to transform businesses through technology.
            </p>

            <div className="flex justify-center space-x-4">
              <a
                href="#about"
                className="bg-purple-600 hover:bg-purple-700 text-white px-6 py-2 rounded-md transition duration-300"
              >
                Our Story
              </a>
              <a
                href="#team"
                className="bg-transparent border border-purple-500 text-purple-400 hover:bg-purple-900 hover:text-white px-6 py-2 rounded-md transition duration-300"
              >
                Meet the Team
              </a>
            </div>
          </div>
        </div>
      </section>

      {/* About Us Section */}
      <section id="about" className="py-20 bg-gray-800">
        <div className="container mx-auto px-6">
          <div className="grid md:grid-cols-2 gap-12 items-center">
            <div>
              <h2 className="text-3xl font-bold mb-6 relative">
                <span className="bg-clip-text text-transparent bg-gradient-to-r from-blue-400 to-purple-500">
                  Our Story
                </span>
                <div className="h-1 w-20 bg-purple-500 mt-2"></div>
              </h2>
              <p className="text-gray-300 mb-4">
                Founded in 2024, OfStartup emerged from a simple yet powerful
                idea: technology should empower businesses, not complicate them.
                What began as a small team of tech enthusiasts in Bangalore has
                grown into a global solutions provider with clients across four
                continents.
              </p>
              <p className="text-gray-300 mb-4">
                Our journey has been defined by continuous innovation and a deep
                commitment to client success. We&apos;ve evolved from offering
                basic web development services to becoming a comprehensive
                technology partner providing advanced AI solutions, business
                strategy, and digital transformation services.
              </p>
              <p className="text-gray-300">
                Today, OfStartup stands at the intersection of technology and
                business strategy, helping organizations of all sizes navigate
                the complex digital landscape and unlock new possibilities for
                growth and innovation.
              </p>
            </div>
            <div className="relative">
              <div className="absolute -inset-4 bg-gradient-to-r from-purple-500 to-blue-500 rounded-lg opacity-30 blur-lg"></div>
              <Card className="bg-gray-900 border-gray-700 overflow-hidden relative z-10">
                <CardContent className="p-0">
                  <div className="p-6">
                    <h3 className="text-2xl font-bold mb-2">Our Mission</h3>
                    <p className="text-gray-300">
                      To empower businesses with technology solutions that drive
                      meaningful transformation and sustainable growth in a
                      rapidly evolving digital world.
                    </p>

                    <div className="mt-6 pt-6 border-t border-gray-700">
                      <div className="grid grid-cols-2 gap-4">
                        <div>
                          <p className="text-purple-400 text-4xl font-bold">
                            120+
                          </p>
                          <p className="text-gray-400">Global Clients</p>
                        </div>
                        <div>
                          <p className="text-purple-400 text-4xl font-bold">
                            250+
                          </p>
                          <p className="text-gray-400">Projects Delivered</p>
                        </div>
                        <div>
                          <p className="text-purple-400 text-4xl font-bold">
                            50+
                          </p>
                          <p className="text-gray-400">Team Members</p>
                        </div>
                        <div>
                          <p className="text-purple-400 text-4xl font-bold">
                            12+
                          </p>
                          <p className="text-gray-400">Countries Served</p>
                        </div>
                      </div>
                    </div>
                  </div>
                </CardContent>
              </Card>
            </div>
          </div>
        </div>
      </section>

      {/* Our Values Section */}
      <section className="py-20 bg-gray-900">
        <div className="container mx-auto px-6">
          <div className="text-center max-w-3xl mx-auto mb-16">
            <h2 className="text-3xl font-bold mb-6">
              <span className="bg-clip-text text-transparent bg-gradient-to-r from-blue-400 to-purple-500">
                Our Values
              </span>
            </h2>
            <p className="text-gray-300">
              These core principles guide everything we do at OfStartup, from
              how we develop solutions to how we build relationships with our
              clients and each other.
            </p>
          </div>

          <div className="grid md:grid-cols-2 lg:grid-cols-4 gap-8">
            {values.map((value, index) => (
              <div
                key={index}
                variants={fadeIn}
                className="bg-gray-800 p-8 rounded-lg border border-gray-700 hover:border-purple-500 transition-all duration-300"
              >
                <div className="text-4xl mb-4">{value.icon}</div>
                <h3 className="text-xl font-bold mb-3 text-purple-400">
                  {value.title}
                </h3>
                <p className="text-gray-300">{value.description}</p>
              </div>
            ))}
          </div>
        </div>
      </section>

      {/* Team Section */}
      <section id="team" className="py-20 bg-gray-800">
        <div className="container mx-auto px-6">
          <div className="text-center max-w-3xl mx-auto mb-16">
            <h2 className="text-3xl font-bold mb-6">
              <span className="bg-clip-text text-transparent bg-gradient-to-r from-blue-400 to-purple-500">
                Meet Our Team
              </span>
            </h2>
            <p className="text-gray-300">
              Our diverse team of experts combines deep technical knowledge with
              business acumen to deliver solutions that drive real impact.
            </p>
          </div>

          <Tabs defaultValue="leadership" className="w-full">
            <TabsList className="grid grid-cols-3 max-w-md mx-auto mb-12">
              <TabsTrigger
                value="leadership"
                className="data-[state=active]:bg-purple-600"
              >
                Leadership
              </TabsTrigger>
              <TabsTrigger
                value="engineering"
                className="data-[state=active]:bg-purple-600"
              >
                Engineering
              </TabsTrigger>
              <TabsTrigger
                value="business"
                className="data-[state=active]:bg-purple-600"
              >
                Business
              </TabsTrigger>
            </TabsList>

            {Object.keys(teamMembers).map((department) => (
              <TabsContent key={department} value={department}>
                <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-8">
                  {teamMembers[department].map((member, index) => (
                    <div key={index} variants={fadeIn}>
                      <Card className="bg-gray-900 border-gray-700 overflow-hidden h-full hover:border-purple-500 transition-all duration-300">
                        <CardContent className="p-0">
                          <div className="relative">
                            <Image
                              fill
                              src={member.image}
                              alt={member.name}
                              className="w-full h-64 object-cover"
                            />
                            <div className="absolute inset-0 bg-gradient-to-t from-gray-900 to-transparent opacity-80"></div>
                            <div className="absolute bottom-0 left-0 p-6">
                              <h3 className="text-xl font-bold text-white">
                                {member.name}
                              </h3>
                              <p className="text-purple-400">{member.role}</p>
                            </div>
                          </div>
                          <div className="p-6">
                            <p className="text-gray-300 mb-4">{member.bio}</p>
                            <a
                              href={member.linkedin}
                              className="text-purple-400 hover:text-purple-300 inline-flex items-center"
                            >
                              Connect on LinkedIn
                              <svg
                                className="w-4 h-4 ml-2"
                                fill="currentColor"
                                viewBox="0 0 24 24"
                              >
                                <path d="M19 0h-14c-2.761 0-5 2.239-5 5v14c0 2.761 2.239 5 5 5h14c2.762 0 5-2.239 5-5v-14c0-2.761-2.238-5-5-5zm-11 19h-3v-11h3v11zm-1.5-12.268c-.966 0-1.75-.79-1.75-1.764s.784-1.764 1.75-1.764 1.75.79 1.75 1.764-.783 1.764-1.75 1.764zm13.5 12.268h-3v-5.604c0-3.368-4-3.113-4 0v5.604h-3v-11h3v1.765c1.396-2.586 7-2.777 7 2.476v6.759z" />
                              </svg>
                            </a>
                          </div>
                        </CardContent>
                      </Card>
                    </div>
                  ))}
                </div>
              </TabsContent>
            ))}
          </Tabs>

          <div className="text-center mt-16">
            <p className="text-gray-300 mb-6">
              Want to join our team of innovators?
            </p>
            <a
              href="/careers"
              className="bg-purple-600 hover:bg-purple-700 text-white px-8 py-3 rounded-md transition duration-300 inline-flex items-center"
            >
              View Open Positions
              <svg
                className="w-4 h-4 ml-2"
                fill="none"
                stroke="currentColor"
                viewBox="0 0 24 24"
              >
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  strokeWidth="2"
                  d="M14 5l7 7m0 0l-7 7m7-7H3"
                />
              </svg>
            </a>
          </div>
        </div>
      </section>

      {/* Global Presence Section */}
      <section className="py-20 bg-gray-900 relative overflow-hidden">
        <div className="absolute inset-0 z-0 bg-[url('/api/placeholder/1920/1080')] bg-cover opacity-5"></div>

        <div className="container mx-auto px-6 relative z-10">
          <div className="text-center max-w-3xl mx-auto mb-16">
            <h2 className="text-3xl font-bold mb-6">
              <span className="bg-clip-text text-transparent bg-gradient-to-r from-blue-400 to-purple-500">
                Global Presence, Local Expertise
              </span>
            </h2>
            <p className="text-gray-300">
              Headquartered in India with a global footprint, we combine the
              best of Indian tech talent with international perspectives.
            </p>
          </div>

          <div className="grid md:grid-cols-2 gap-12 items-center">
            <div className="bg-gray-800 p-8 rounded-lg border border-gray-700">
              <h3 className="text-2xl font-bold mb-4 text-purple-400">
                Our Headquarters
              </h3>
              <p className="text-gray-300 mb-6">
                Our main office in Bangalore serves as the innovation hub where
                most of our engineering and product teams collaborate.
              </p>

              <div className="mb-6">
                <h4 className="font-bold text-gray-200 mb-2">
                  Bangalore Office
                </h4>
                <p className="text-gray-300">
                  OfStartup Technologies
                  <br />
                  123 Tech Park, Electronic City
                  <br />
                  Bangalore, Karnataka 560100
                  <br />
                  India
                </p>
              </div>

              <h4 className="font-bold text-gray-200 mb-2">
                Satellite Offices
              </h4>
              <div className="grid grid-cols-2 gap-4">
                <div>
                  <p className="text-gray-300">Mumbai, India</p>
                </div>
                <div>
                  <p className="text-gray-300">Delhi NCR, India</p>
                </div>
                <div>
                  <p className="text-gray-300">Singapore</p>
                </div>
                <div>
                  <p className="text-gray-300">London, UK</p>
                </div>
              </div>
            </div>

            <div>
              <Image
                fill
                src="/api/placeholder/600/400"
                alt="OfStartup global map"
                className="w-full h-auto rounded-lg border border-gray-700"
              />
            </div>
          </div>
        </div>
      </section>

      {/* CTA Section */}
      <section className="py-20 bg-gradient-to-br from-purple-900 via-blue-900 to-gray-900">
        <div className="container mx-auto px-6">
          <div className="text-center max-w-3xl mx-auto">
            <h2 className="text-3xl font-bold mb-6 text-white">
              Ready to Transform Your Business?
            </h2>
            <p className="text-gray-200 mb-8">
              Let our team of experts help you navigate the digital landscape
              and unlock new possibilities for growth.
            </p>
            <div className="flex flex-col sm:flex-row justify-center space-y-4 sm:space-y-0 sm:space-x-4">
              <a
                href="/contact"
                className="bg-white text-purple-900 hover:bg-gray-100 px-8 py-3 rounded-md transition duration-300 font-medium"
              >
                Schedule a Consultation
              </a>
              <a
                href="/solutions"
                className="bg-transparent border border-white text-white hover:bg-white hover:text-purple-900 px-8 py-3 rounded-md transition duration-300 font-medium"
              >
                Explore Our Solutions
              </a>
            </div>
          </div>
        </div>
      </section>
    </div>
  );
};

export default AboutPage;
